@extends('layouts.app')

@section('title', 'Menu Addons')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4>Menu Addons</h4>
        <button class="btn btn-primary" onclick="openModal('createAddonModal', 'Create Addon')">Create New</button>
    </div>
    <div class="card-body">
        <table id="addonsTable" class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Menu Item</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createAddonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Addon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAddonForm" action="{{ route('addons.store') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">Addon Group Name</label>
                        <input type="text" name="addon_group_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Price</label>
                        <input type="number" step="0.01" name="price" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cost</label>
                        <input type="number" step="0.01" name="cost" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Menu Item</label>
                        <select name="menu_item_id" class="form-control" required>
                            <!-- Options loaded dynamically -->
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editAddonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Addon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Form loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        const table = initDataTable('addonsTable', '{{ route('addons.index') }}', [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'price', name: 'price' },
            { data: 'menu_item_name', name: 'menu_item_name' },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false 
            }
        ]);

        // Load menu items for create form
        loadMenuItems();

        // Handle create form submission
        submitForm('createAddonForm', function(data) {
            $('#createAddonModal').modal('hide');
            table.ajax.reload();
            Toast.success('Addon created successfully');
        }, function(errors) {
            console.log(errors);
        });

        // Handle edit button clicks
        $(document).on('click', '.edit-addon', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('addons.show', ':id') }}'.replace(':id', id);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const addon = data.data;
                        $('#editAddonModal .modal-body').html(`
                            <form id="editAddonForm" action="{{ route('addons.update', ':id') }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label class="form-label">Addon Group Name</label>
                                    <input type="text" name="addon_group_name" class="form-control" value="${addon.addon_group_name || ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Name</label>
                                    <input type="text" name="name" class="form-control" value="${addon.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Price</label>
                                    <input type="number" step="0.01" name="price" class="form-control" value="${addon.price}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Cost</label>
                                    <input type="number" step="0.01" name="cost" class="form-control" value="${addon.cost || ''}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Menu Item</label>
                                    <select name="menu_item_id" class="form-control" required>
                                        <option value="${addon.menu_item_id}" selected>${addon.menu_item_name}</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Update</button>
                            </form>
                        `.replace(':id', id));
                        
                        // Load menu items for edit form
                        loadMenuItems('#editAddonForm select[name="menu_item_id"]', addon.menu_item_id);
                        
                        $('#editAddonModal').modal('show');
                        
                        // Handle edit form submission
                        submitForm('editAddonForm', function(data) {
                            $('#editAddonModal').modal('hide');
                            table.ajax.reload();
                            Toast.success('Addon updated successfully');
                        }, function(errors) {
                            console.log(errors);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('Failed to load addon data');
                });
        });

        // Handle delete button clicks
        $(document).on('click', '.delete-addon', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('addons.destroy', ':id') }}'.replace(':id', id);
            
            confirmDelete(url, function() {
                table.ajax.reload();
            });
        });

        // Function to load menu items
        function loadMenuItems(selector = '#createAddonForm select[name="menu_item_id"]', selectedId = null) {
            fetch('{{ route('menu-items.index') }}?all=true')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = $(selector);
                        select.empty();
                        select.append('<option value="">Select Menu Item</option>');
                        
                        data.data.forEach(item => {
                            const selected = selectedId && selectedId == item.id ? 'selected' : '';
                            select.append(`<option value="${item.id}" ${selected}>${item.name}</option>`);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading menu items:', error);
                });
        }
    });
</script>
@endpush
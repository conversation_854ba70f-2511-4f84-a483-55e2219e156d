<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Models\Menu;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuRequest;
use Modules\Menu\Http\Requests\UpdateMenuRequest;
use Yajra\DataTables\Facades\DataTables;

class MenuWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    public function index(Request $request)
    {
        $user = auth()->user();
        
        if ($request->ajax()) {
            // Filter menus by current user's branch
            $menus = Menu::where('branch_id', $user->branch_id)
                ->select(['id', 'name', 'menu_type', 'is_active']);
            
            return DataTables::of($menus)
                ->addIndexColumn()
              
                ->addColumn('status', function($menu) {
                    return $menu->is_active 
                        ? '<span class="badge bg-success">Active</span>' 
                        : '<span class="badge bg-danger">Inactive</span>';
                })
                ->addColumn('actions', function($menu) {
                    return '<div class="btn-group">' .
                        '<button type="button" class="btn btn-sm btn-primary edit-btn" data-id="' . $menu->id . '" title="Edit"><i class="fas fa-edit"></i></button>' .
                        '<button type="button" class="btn btn-sm btn-danger delete-btn" data-id="' . $menu->id . '" title="Delete"><i class="fas fa-trash"></i></button>' .
                    '</div>';
                })
                ->rawColumns(['actions', 'status'])
                ->make(true);
        }
      
        return view('menu::menus.index');
    }
    
    public function store(StoreMenuRequest $request)
    {
        try {
            $menu = $this->menuService->createMenu($request->validated());
            
            return response()->json([
                'success' => true,
                'message' => 'Menu created successfully',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu: ' . $e->getMessage()
            ], 500);
        }
    }
    
    public function edit($id)
    {
        try {
            $menu = Menu::with(['tenant', 'branch'])->findOrFail($id);
            return response()->json($menu);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Menu not found'
            ], 404);
        }
    }
    
    public function update(UpdateMenuRequest $request, $id)
    {
        try {
            $menu = Menu::findOrFail($id);
            $menu = $this->menuService->updateMenu($menu, $request->validated());
            
            return response()->json([
                'success' => true,
                'message' => 'Menu updated successfully',
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu: ' . $e->getMessage()
            ], 500);
        }
    }
    
    public function destroy($id)
    {
        try {
            $menu = Menu::findOrFail($id);
            
            // Check if menu has associated items
            if ($menu->menuItems()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete menu with associated menu items'
                ], 422);
            }
            
            $this->menuService->deleteMenu($menu);
            
            return response()->json([
                'success' => true,
                'message' => 'Menu deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get branches for a specific tenant (AJAX endpoint)
     */
    public function getBranchesByTenant(Request $request)
    {
        $tenantId = $request->get('tenant_id');
        
        if (!$tenantId) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant ID is required'
            ], 400);
        }

        $branches = Branch::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->get(['id', 'name']);

        return response()->json([
            'success' => true,
            'data' => $branches
        ]);
    }
}
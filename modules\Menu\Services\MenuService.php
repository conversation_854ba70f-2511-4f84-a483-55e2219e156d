<?php

namespace Modules\Menu\Services;

use App\Models\Menu;
use App\Models\MenuItem;

use App\Models\MenuCategory;

use App\Models\MenuItemAddon;

use App\Models\MenuItemBranch;
use App\Models\MenuItemVariant;
use App\Models\MenuAvailability;
use Illuminate\Support\Facades\DB;
use Modules\Menu\Helpers\MenuHelper;
use Modules\Menu\Services\PricingService;
use Illuminate\Database\Eloquent\Collection;
use Modules\Menu\Services\AvailabilityService;
use Illuminate\Pagination\LengthAwarePaginator;

class MenuService
{
    protected $pricingService;
    protected $availabilityService;

    public function __construct(PricingService $pricingService, AvailabilityService $availabilityService)
    {
        $this->pricingService = $pricingService;
        $this->availabilityService = $availabilityService;
    }
    /**
     * Get all menu items with pagination and filters
     */
    public function getAllMenuItems(array $filters = []): LengthAwarePaginator
    {
        $query = MenuItem::with(['category', 'variants', 'addons']);

        // Apply filters
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_available'])) {
            $query->where('is_available', $filters['is_available']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['price_min'])) {
            $query->where('price', '>=', $filters['price_min']);
        }

        if (isset($filters['price_max'])) {
            $query->where('price', '<=', $filters['price_max']);
        }

        return $query->orderBy('category_id')
                    ->orderBy('sort_order')
                    ->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get menu item by ID
     */
    public function getMenuItemById(string $id): MenuItem
    {
        return MenuItem::with(['category', 'variants', 'addons', 'branches'])
            ->findOrFail($id);
    }

    /**
     * Create a new menu
     */
    public function createMenu(array $data): Menu
    {
        return Menu::create([
            // 'tenant_id' => $data['tenant_id'],
            // 'branch_id' => $data['branch_id'],
            'tenant_id' => auth()->user()->tenant_id ?? null,
            'branch_id' => auth()->user()->branch_id ?? null,
            'name' => $data['name'],
            'code' => $data['code'],
            'description' => $data['description'] ?? null,
            'menu_type' => $data['menu_type'] ?? 'main',
            'start_time' => $data['start_time'] ?? null,
            'end_time' => $data['end_time'] ?? null,
            'available_days' => $data['available_days'] ?? null,
            'is_active' => $data['is_active'] ?? true,
            'is_default' => $data['is_default'] ?? false,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);
    }

    /**
     * Update an existing menu
     */
    public function updateMenu(Menu $menu, array $data): Menu
    {
        $menu->update($data);
        return $menu;
    }

    /**
     * Delete a menu
     */
    public function deleteMenu(string $id): void
    {
        $menu = Menu::findOrFail($id);
        $menu->delete();
    }

    /**
     * Get menus for a branch
     */
    public function getMenusForBranch(string $branchId): Collection
    {
        return Menu::where('branch_id', $branchId)
            ->with(['categories', 'menuItems'])
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get categories for a specific menu
     */
    public function getMenuCategories(string $menuId): Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->select('id', 'name', 'code', 'description')
            ->get();
    }

    /**
     * Create a new menu item
     */
    public function createMenuItem(array $data): MenuItem
    {
        $menuItem = MenuItem::create([
            'menu_id' => $data['menu_id'],
            'name' => $data['name'],
            'code' => $data['code'],
            'description' => $data['description'] ?? null,
            'short_description' => $data['short_description'] ?? null,
            'category_id' => $data['category_id'],
            'base_price' => $data['base_price'],
            'cost_price' => $data['cost_price'] ?? null,
            'prep_time_minutes' => $data['prep_time_minutes'] ?? 0,
            'calories' => $data['calories'] ?? null,
            'nutritional_info' => $data['nutritional_info'] ?? null,
            'allergens' => $data['allergens'] ?? null,
            'dietary_info' => $data['dietary_info'] ?? null,
            'recipe_id' => $data['recipe_id'] ?? null,
            'barcode' => $data['barcode'] ?? null,
            'sku' => $data['sku'] ?? null,
            'is_active' => $data['is_active'] ?? true,
            'is_featured' => $data['is_featured'] ?? false,
            'is_spicy' => $data['is_spicy'] ?? false,
            'spice_level' => $data['spice_level'] ?? null,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);

        // Handle variants if provided
        if (isset($data['variants']) && is_array($data['variants'])) {
            $this->createMenuItemVariants($menuItem, $data['variants']);
        }

        // Handle addons if provided
        if (isset($data['addons']) && is_array($data['addons'])) {
            $this->createMenuItemAddons($menuItem, $data['addons']);
        }

        return $menuItem->load(['category', 'variants', 'addons']);
    }

    /**
     * Update an existing menu item
     */
    public function updateMenuItem(string $id, array $data): MenuItem
    {
        
        $menuItem = MenuItem::findOrFail($id);
        
        $menuItem->update([
            
            'name' => $data['name'] ?? $menuItem->name,
            'menu_id' => $data['menu_id'] ?? $menuItem->menu_id,
            'code' => $data['code'] ?? $menuItem->code,
            'description' => $data['description'] ?? $menuItem->description,
            'short_description' => $data['short_description'] ?? $menuItem->short_description,
            'category_id' => $data['category_id'] ?? $menuItem->category_id,
            'base_price' => $data['base_price'] ?? $menuItem->base_price,
            'cost_price' => $data['cost_price'] ?? $menuItem->cost_price,
            'prep_time_minutes' => $data['prep_time_minutes'] ?? $menuItem->prep_time_minutes,
            'calories' => $data['calories'] ?? $menuItem->calories,
            'nutritional_info' => $data['nutritional_info'] ?? $menuItem->nutritional_info,
            'allergens' => $data['allergens'] ?? $menuItem->allergens,
            'dietary_info' => $data['dietary_info'] ?? $menuItem->dietary_info,
            'recipe_id' => $data['recipe_id'] ?? $menuItem->recipe_id,
            'barcode' => $data['barcode'] ?? $menuItem->barcode,
            'sku' => $data['sku'] ?? $menuItem->sku,
            'is_active' => $data['is_active'] ?? $menuItem->is_active,
            'is_featured' => $data['is_featured'] ?? $menuItem->is_featured,
            'is_spicy' => $data['is_spicy'] ?? $menuItem->is_spicy,
            'spice_level' => $data['spice_level'] ?? $menuItem->spice_level,
            'sort_order' => $data['sort_order'] ?? $menuItem->sort_order,
        ]);

        return $menuItem->load(['category', 'variants', 'addons']);
    }

    /**
     * Delete a menu item
     */
    public function deleteMenuItem(string $id): void
    {
        $menuItem = MenuItem::findOrFail($id);
        $menuItem->variants()->delete();
        $menuItem->addons()->delete();
        $menuItem->delete();
    }

    /**
     * Create a new menu category
     */
    public function createCategory(array $data): MenuCategory
    {
        return MenuCategory::create([
            'menu_id' => $data['menu_id'],
            'parent_category_id' => $data['parent_category_id'] ?? null,
            'name' => $data['name'],
            'code' => $data['code'],
            'description' => $data['description'] ?? null,
            'image_url' => $data['image_url'] ?? null,
            'sort_order' => $data['sort_order'] ?? 0,
            'is_active' => $data['is_active'] ?? true,
        ]);
    }

    /**
     * Get all menu categories for a menu
     */
    public function getCategories(string $menuId = null): Collection
    {
        $query = MenuCategory::orderBy('sort_order');
        
        if ($menuId) {
            $query->where('menu_id', $menuId);
        }
        
        return $query->get();
    }

    /**
     * Get category by ID
     */
    public function getCategoryById(string $id): MenuCategory
    {
        return MenuCategory::findOrFail($id);
    }

    /**
     * Get menu items by category
     */
    public function getItemsByCategory(string $categoryId): Collection
    {
        return MenuItem::with(['variants', 'addons'])
            ->where('category_id', $categoryId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Toggle menu item availability
     */
    public function toggleAvailability(string $id): MenuItem
    {
        $menuItem = MenuItem::findOrFail($id);
        $menuItem->update(['is_active' => !$menuItem->is_active]);
        
        return $menuItem;
    }

    /**
     * Create menu item variants
     */
    private function createMenuItemVariants(MenuItem $menuItem, array $variants): void
    {
        foreach ($variants as $variant) {
            // Using the relationship but creating with Variant model attributes
            $menuItem->variants()->create([
                'name' => $variant['name'],
                'code' => $variant['code'],
                'price_modifier' => $variant['price_modifier'] ?? 0,
                'cost_modifier' => $variant['cost_modifier'] ?? 0,
                'is_default' => $variant['is_default'] ?? false,
                'sort_order' => $variant['sort_order'] ?? 0,
            ]);
        }
    }

    /**
     * Create menu item addons
     */
    private function createMenuItemAddons(MenuItem $menuItem, array $addons): void
    {
        foreach ($addons as $addon) {
            // Using the relationship but creating with Addon model attributes
            $menuItem->addons()->create([
                'addon_group_name' => $addon['addon_group_name'] ?? null,
                'name' => $addon['name'],
                'code' => $addon['code'],
                'price' => $addon['price'] ?? 0,
                'cost' => $addon['cost'] ?? 0,
                'is_required' => $addon['is_required'] ?? false,
                'max_quantity' => $addon['max_quantity'] ?? 1,
                'sort_order' => $addon['sort_order'] ?? 0,
            ]);
        }
    }

    /**
     * Get featured menu items
     */
    public function getFeaturedItems(): Collection
    {
        return MenuItem::with(['category'])
            ->where('is_featured', true)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Search menu items
     */
    public function searchMenuItems(string $query): Collection
    {
        return MenuItem::with(['category'])
            ->where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%')
                  ->orWhere('short_description', 'like', '%' . $query . '%');
            })
            ->orderBy('name')
            ->get();
    }

    // ==================== CATEGORY MANAGEMENT ====================

    /**
     * Create a new menu category
     */
    // public function createCategory(array $data): MenuCategory
    // {
    //     $categoryData = [
    //         'parent_category_id' => $data['parent_category_id'] ?? null,
    //         'name' => $data['name'],
    //         'code' => $data['code'] ?? "null",
    //         'description' => $data['description'] ?? null,
    //         'image_url' => $data['image_url'] ?? null,
    //         'sort_order' => $data['sort_order'] ?? 0,
    //         'is_active' => $data['is_active'] ?? true,
            
    //     ];
        
    //     // Only add tenant_id if it's provided
    //     if (isset($data['tenant_id'])) {
    //         $categoryData['tenant_id'] = $data['tenant_id'] ?? 1;
    //     }
        
    //     return MenuCategory::create($categoryData);
    // }

    /**
     * Update a menu category
     */
    public function updateCategory(string $id, array $data): MenuCategory
    {
        $category = MenuCategory::findOrFail($id);
        $category->update($data);
        return $category;
    }

    /**
     * Delete a menu category
     */
    public function deleteCategory(string $id): void
    {
        $category = MenuCategory::findOrFail($id);
        
        // Check if category has menu items
        if ($category->menuItems()->count() > 0) {
            throw new \Exception('Cannot delete category with existing menu items');
        }
        
        $category->delete();
    }

    /**
     * Get category with its menu items
     */
    public function getCategoryWithItems(string $id): MenuCategory
    {
        return MenuCategory::with(['menuItems.variants', 'menuItems.addons'])
            ->findOrFail($id);
    }

    // ==================== VARIANT MANAGEMENT ====================

    /**
     * Create menu item variant
     */
    public function createVariant(string $menuItemId, array $data): MenuItemVariant
    {
        $menuItem = MenuItem::findOrFail($menuItemId);
        
        return $menuItem->variants()->create([
            'name' => $data['name'],
            'code' => $data['code'],
            'price_modifier' => $data['price_modifier'] ?? 0,
            'cost_modifier' => $data['cost_modifier'] ?? 0,
            'is_default' => $data['is_default'] ?? false,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);
    }

    /**
     * Update menu item variant
     */
    public function updateVariant(string $variantId, array $data): MenuItemVariant
    {
        $variant = MenuItemVariant::findOrFail($variantId);
        $variant->update($data);
        return $variant;
    }

    /**
     * Delete menu item variant
     */
    public function deleteVariant(string $variantId): void
    {
        $variant = MenuItemVariant::findOrFail($variantId);
        $variant->delete();
    }

    /**
     * Get variants for a menu item
     */
    public function getMenuItemVariants(string $menuItemId): Collection
    {
        return MenuItemVariant::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->get();
    }

    // ==================== ADDON MANAGEMENT ====================

    /**
     * Create menu item addon
     */
    public function createAddon(string $menuItemId, array $data): MenuItemAddon
    {
        $menuItem = MenuItem::findOrFail($menuItemId);
        
        return $menuItem->addons()->create([
            'addon_group_name' => $data['addon_group_name'] ?? null,
            'name' => $data['name'],
            'code' => $data['code'],
            'price' => $data['price'] ?? 0,
            'cost' => $data['cost'] ?? 0,
            'is_required' => $data['is_required'] ?? false,
            'max_quantity' => $data['max_quantity'] ?? 1,
            'sort_order' => $data['sort_order'] ?? 0,
        ]);
    }

    /**
     * Update menu item addon
     */
    public function updateAddon(string $addonId, array $data): MenuItemAddon
    {
        $addon = MenuItemAddon::findOrFail($addonId);
        $addon->update($data);
        return $addon;
    }

    /**
     * Delete menu item addon
     */
    public function deleteAddon(string $addonId): void
    {
        $addon = MenuItemAddon::findOrFail($addonId);
        $addon->delete();
    }

    /**
     * Get addons for a menu item
     */
    public function getMenuItemAddons(string $menuItemId): Collection
    {
        return MenuItemAddon::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->get();
    }

    // ==================== AVAILABILITY MANAGEMENT ====================

    /**
     * Set menu item availability schedule
     */
    public function setAvailabilitySchedule(string $menuItemId, array $scheduleData): MenuAvailability
    {
        return $this->availabilityService->setAvailabilitySchedule($menuItemId, $scheduleData);
    }

    /**
     * Check if menu item is available at specific time
     */
    public function isAvailableAt(string $menuItemId, string $date, string $time = null, ?string $branchId = null): bool
    {
        return $this->availabilityService->isAvailableAt($menuItemId, $date, $time, $branchId);
    }

    /**
     * Get available menu items based on filters
     */
    public function getAvailableMenuItems(array $filters = []): Collection
    {
        $date = $filters['date'] ?? null;
        $time = $filters['time'] ?? null;
        $branchId = $filters['branch_id'] ?? null;
        
        return $this->availabilityService->getAvailableMenuItems($date, $time, $branchId);
    }

    /**
     * Forecast weekly availability for menu item
     */
    public function forecastWeeklyAvailability(string $menuItemId, ?string $branchId = null): array
    {
        return $this->availabilityService->forecastWeeklyAvailability($menuItemId, $branchId);
    }

    /**
     * Set branch-specific availability for menu item
     */
    public function setBranchAvailability(string $menuItemId, string $branchId, bool $isAvailable): void
    {
        $this->availabilityService->setBranchAvailability($menuItemId, $branchId, $isAvailable);
    }

    /**
     * Bulk update availability status of multiple menu items
     */
    public function bulkUpdateAvailability(array $menuItemIds, bool $isAvailable): int
    {
        return $this->availabilityService->bulkUpdateAvailability($menuItemIds, $isAvailable);
    }

    /**
     * Check for conflicts in availability schedule
     */
    public function checkAvailabilityConflicts(string $menuItemId, array $schedule, ?string $branchId = null): array
    {
        return $this->availabilityService->checkAvailabilityConflicts($menuItemId, $schedule, $branchId);
    }

    /**
     * Get availability statistics
     */
    public function getAvailabilityStatistics(): array
    {
        return $this->availabilityService->getAvailabilityStatistics();
    }

    // ==================== BRANCH-SPECIFIC OPERATIONS ====================

    /**
     * Set branch-specific menu item settings
     */
    public function setBranchSettings(string $menuItemId, string $branchId, array $data): MenuItemBranch
    {
        return MenuItemBranch::updateOrCreate(
            [
                'menu_item_id' => $menuItemId,
                'branch_id' => $branchId,
            ],
            [
                'branch_price' => $data['branch_price'] ?? null,
                'is_available' => $data['is_available'] ?? true,
                'stock_quantity' => $data['stock_quantity'] ?? null,
                'low_stock_threshold' => $data['low_stock_threshold'] ?? null,
            ]
        );
    }

    /**
     * Get menu items for specific branch
     */
    public function getMenuForBranch(string $branchId, array $filters = []): Collection
    {
        $query = MenuItem::with(['category', 'variants', 'addons'])
            ->leftJoin('menu_item_branches', function ($join) use ($branchId) {
                $join->on('menu_items.id', '=', 'menu_item_branches.menu_item_id')
                     ->where('menu_item_branches.branch_id', $branchId);
            })
            ->where('menu_items.is_active', true)
            ->where(function ($query) {
                $query->whereNull('menu_item_branches.is_available')
                      ->orWhere('menu_item_branches.is_available', true);
            });
            
        if (isset($filters['category_id'])) {
            $query->where('menu_items.category_id', $filters['category_id']);
        }
        
        return $query->select('menu_items.*')
            ->orderBy('menu_items.category_id')
            ->orderBy('menu_items.sort_order')
            ->get();
    }

    /**
     * Get menu item price for specific branch
     */
    public function getMenuItemPriceForBranch(string $menuItemId, string $branchId): float
    {
        return $this->pricingService->getMenuItemPrice($menuItemId, $branchId);
    }

    /**
     * Calculate total price with modifiers
     */
    public function calculateTotalPrice(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null): float
    {
        return $this->pricingService->calculateTotalPrice($menuItemId, $variants, $addons, $branchId);
    }

    /**
     * Get pricing breakdown
     */
    public function getPricingBreakdown(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null): array
    {
        return $this->pricingService->getPricingBreakdown($menuItemId, $variants, $addons, $branchId);
    }

    // ==================== MENU MANAGEMENT ====================

    /**
     * Get all menus with filters
     */
    public function getAllMenus(array $filters = []): Collection
    {
        $query = Menu::with(['tenant', 'branch', 'categories', 'menuItems']);

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (isset($filters['menu_type'])) {
            $query->where('menu_type', $filters['menu_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('sort_order')
                    ->orderBy('name')
                    ->get();
    }

    /**
     * Get menu by ID
     */
    public function getMenuById(string $id): Menu
    {
        return Menu::with(['tenant', 'branch', 'categories.menuItems', 'menuItems.variants', 'menuItems.addons'])
            ->findOrFail($id);
    }

    /**
     * Get all variants with menu item information
     */
    public function getAllVariants(): Collection
    {
        return \App\Models\MenuItemVariant::with(['menuItem.category'])
            ->orderBy('menu_item_id')
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get all addons with menu item information
     */
    public function getAllAddons(): Collection
    {
        return \App\Models\MenuItemAddon::with(['menuItem.category'])
            ->orderBy('menu_item_id')
            ->orderBy('sort_order')
            ->get();
    }

    // ==================== UTILITY FUNCTIONS ====================

    /**
     * Bulk update menu item status
     */
    public function bulkUpdateStatus(array $menuItemIds, bool $isActive): int
    {
        return MenuItem::whereIn('id', $menuItemIds)
            ->update(['is_active' => $isActive]);
    }

    /**
     * Get menu statistics
     */
    public function getMenuStatistics(): array
    {
        return [
            'total_items' => MenuItem::count(),
            'active_items' => MenuItem::where('is_active', true)->count(),
            'featured_items' => MenuItem::where('is_featured', true)->count(),
            'total_categories' => MenuCategory::count(),
        'active_categories' => MenuCategory::where('is_active', true)->count(),
            'items_with_variants' => MenuItem::has('variants')->count(),
            'items_with_addons' => MenuItem::has('addons')->count(),
        ];
    }

    /**
     * Get low stock menu items for branch
     */
    public function getLowStockItems(string $branchId): Collection
    {
        return MenuItem::with(['category'])
            ->join('menu_item_branches', 'menu_items.id', '=', 'menu_item_branches.menu_item_id')
            ->where('menu_item_branches.branch_id', $branchId)
            ->whereNotNull('menu_item_branches.stock_quantity')
            ->whereNotNull('menu_item_branches.low_stock_threshold')
            ->whereRaw('menu_item_branches.stock_quantity <= menu_item_branches.low_stock_threshold')
            ->select('menu_items.*', 'menu_item_branches.stock_quantity', 'menu_item_branches.low_stock_threshold')
            ->get();
    }
}
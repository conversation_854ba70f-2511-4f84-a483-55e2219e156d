<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreVariantRequest;
use Modules\Menu\Http\Requests\UpdateVariantRequest;
use Yajra\DataTables\Facades\DataTables;

class VariantWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $variants = $this->menuService->getAllVariants();
            
            return DataTables::of($variants)
                ->addColumn('menu_item_name', function ($variant) {
                    return $variant->menuItem ? $variant->menuItem->name : 'N/A';
                })
                ->addColumn('category_name', function ($variant) {
                    return $variant->menuItem && $variant->menuItem->category ? $variant->menuItem->category->name : 'N/A';
                })
                ->addColumn('price_modifier_formatted', function ($variant) {
                    return '$' . number_format($variant->price_modifier, 2);
                })
                ->addColumn('actions', function ($variant) {
                    return '
                        <button type="button" class="btn btn-sm btn-primary edit-variant" 
                                data-id="' . $variant->id . '">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-variant" 
                                data-id="' . $variant->id . '">
                            <i class="fas fa-trash"></i>
                        </button>
                    ';
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('menu::variants.index');
    }

    public function store(StoreVariantRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $variant = $this->menuService->createVariant($data['menu_item_id'], $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Variant created successfully',
                'data' => $variant
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating variant: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(string $id): JsonResponse
    {
        try {
            $variant = \App\Models\MenuItemVariant::with(['menuItem.category'])->findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $variant
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Variant not found'
            ], 404);
        }
    }

    public function update(UpdateVariantRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $variant = $this->menuService->updateVariant($id, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Variant updated successfully',
                'data' => $variant
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating variant: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(string $id): JsonResponse
    {
        try {
            $this->menuService->deleteVariant($id);
            return response()->json([
                'success' => true,
                'message' => 'Variant deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting variant: ' . $e->getMessage()
            ], 500);
        }
    }
}
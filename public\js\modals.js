// Global Modals JS for CRUD operations
// Uses Bootstrap 5 modals and Fetch API for AJAX

// Function to open a modal with content loaded via AJAX
function openModal(modalId, title, url = null, method = 'GET', data = {}) {
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    const modalTitle = document.querySelector(`#${modalId} .modal-title`);
    const modalBody = document.querySelector(`#${modalId} .modal-body`);
    const form = document.querySelector(`#${modalId} form`);

    modalTitle.textContent = title;

    if (url) {
        // Load content via AJAX
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: method !== 'GET' ? JSON.stringify(data) : null
        })
        .then(response => response.text())
        .then(html => {
            modalBody.innerHTML = html;
            modal.show();
        })
        .catch(error => console.error('Error loading modal content:', error));
    } else {
        // Clear form for create
        if (form) form.reset();
        modal.show();
    }
}

// Function to handle form submission via AJAX
function submitForm(formId, successCallback, errorCallback) {
    const form = document.getElementById(formId);
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(form);
        const url = form.action;
        const method = form.method;

        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Toast.success(data.message || 'Operation completed successfully');
                successCallback(data);
            } else {
                if (typeof data.errors === 'object') {
                    // Display validation errors
                    for (const field in data.errors) {
                        Toast.error(data.errors[field][0]);
                    }
                } else {
                    Toast.error(data.message || 'An error occurred');
                }
                errorCallback(data.errors);
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            Toast.error('An error occurred while processing your request');
        });
    });
}

// Function to confirm deletion
function confirmDelete(url, successCallback) {
    Confirm.delete(function() {
        fetch(url, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Toast.success('Item deleted successfully');
                successCallback(data);
            } else {
                Toast.error('Deletion failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting item:', error);
            Toast.error('An error occurred while deleting the item');
        });
    });
}

// Initialize DataTables with AJAX and CRUD buttons
function initDataTable(tableId, ajaxUrl, columns, options = {}) {
    const defaultOptions = {
        processing: true,
        serverSide: true,
        ajax: ajaxUrl,
        columns: columns,
        // responsive: true,
        pageLength: 25,
        autoWidth: false, // ✅ Avoids auto-calculating weird widths
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Loading...",
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        }
    };

    const tableOptions = { ...defaultOptions, ...options };
    
    return $(`#${tableId}`).DataTable(tableOptions);
}

// Example usage:
// openModal('createModal', 'Create New Item');
// submitForm('crudForm', (data) => { console.log('Success:', data); }, (errors) => { console.log('Errors:', errors); });
// initDataTable('exampleTable', '/data', [{data: 'name'}, {data: 'email'}]);
// Notifications.js - <PERSON>les toast notifications and modal confirmations

// Configure Toastr defaults
toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    preventDuplicates: true,
    newestOnTop: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut"
};

// Toast notification functions
const Toast = {
    success: function(message) {
        toastr.success(message, 'Success');
    },
    error: function(message) {
        toastr.error(message, 'Error');
    },
    warning: function(message) {
        toastr.warning(message, 'Warning');
    },
    info: function(message) {
        toastr.info(message, 'Information');
    }
};

// Confirmation modal functions
const Confirm = {
    // Show a confirmation modal instead of the browser's confirm dialog
    show: function(title, message, confirmCallback, cancelCallback = null) {
        // Create modal if it doesn't exist
        if (!document.getElementById('confirmationModal')) {
            const modalHTML = `
                <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="confirmationModalLabel">Confirmation</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                Are you sure you want to proceed?
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="confirmCancel">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmOk">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = modalHTML;
            document.body.appendChild(modalContainer.firstChild);
        }
        
        // Get modal elements
        const modal = document.getElementById('confirmationModal');
        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        const confirmBtn = document.getElementById('confirmOk');
        const cancelBtn = document.getElementById('confirmCancel');
        
        // Set modal content
        modalTitle.textContent = title;
        modalBody.textContent = message;
        
        // Create Bootstrap modal instance
        const modalInstance = new bootstrap.Modal(modal);
        
        // Set up event handlers
        const handleConfirm = function() {
            modalInstance.hide();
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            if (confirmCallback) confirmCallback();
        };
        
        const handleCancel = function() {
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            if (cancelCallback) cancelCallback();
        };
        
        // Add event listeners
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        modal.addEventListener('hidden.bs.modal', handleCancel, { once: true });
        
        // Show the modal
        modalInstance.show();
    },
    
    // Shorthand for delete confirmation
    delete: function(confirmCallback, cancelCallback = null) {
        this.show(
            'Confirm Deletion', 
            'Are you sure you want to delete this item? This action cannot be undone.',
            confirmCallback,
            cancelCallback
        );
    }
};

// Export the objects for global use
window.Toast = Toast;
window.Confirm = Confirm;
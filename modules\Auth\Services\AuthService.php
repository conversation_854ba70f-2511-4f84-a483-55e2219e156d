<?php

namespace Modules\Auth\Services;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AuthService
{
    /**
     * Register a new user
     */
    public function register(array $data): array
    {
        
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'tenant_id' => $data['tenant_id'] ?? null,
            'branch_id' => $data['branch_id'] ?? null,
          
            'salary' => $data['salary'] ?? null,
            'email_verified_at' => now(), // Auto-verify for API registration
        ]);

        // Assign default role if provided
        if (isset($data['role'])) {
            $user->assignRole($data['role']);
        }

        // Create token with Sanctum
        $token = $user->createToken('auth-token');
        
        return [
            'user' => $user->load('roles', 'tenant', 'branch'),
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
            'expires_at' => null // Sanctum tokens don't expire by default
        ];
    }

    /**
     * Login user and create token
     */
    public function login(array $credentials): ?array
    {
        if (!Auth::attempt(['email' => $credentials['email'], 'password' => $credentials['password']])){
            return null;
        }

        $user = Auth::user();
        
        // Revoke existing tokens if not remember_me
        if (!isset($credentials['remember_me']) || !$credentials['remember_me']) {
            $user->tokens()->delete();
        }
        
        // Create token with Sanctum
        $token = $user->createToken('auth-token');

        return [
            'user' => $user->load('roles', 'tenant', 'branch'),
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
            'expires_at' => null // Sanctum tokens don't expire by default
        ];
    }

    /**
     * Logout user (revoke current token)
     */
    public function logout(User $user): bool
    {
        $user->currentAccessToken()->delete();
        return true;
    }

    /**
     * Logout from all devices (revoke all tokens)
     */
    public function logoutAll(User $user): bool
    {
        $user->tokens()->delete();
        return true;
    }

    /**
     * Send password reset link
     */
    public function sendPasswordResetLink(array $data): bool
    {
        $status = Password::sendResetLink(['email' => $data['email']]);
        
        return $status === Password::RESET_LINK_SENT;
    }

    /**
     * Reset password
     */
    public function resetPassword(array $data): bool
    {
        $status = Password::reset(
            [
                'email' => $data['email'],
                'password' => $data['password'],
                'password_confirmation' => $data['password_confirmation'],
                'token' => $data['token']
            ],
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                    'remember_token' => Str::random(60),
                ])->save();

                // Revoke all existing tokens
                $user->tokens()->delete();
            }
        );

        return $status === Password::PASSWORD_RESET;
    }

    /**
     * Refresh access token
     */
    public function refreshToken(User $user): array
    {
        // Delete current token
        $user->currentAccessToken()->delete();
        
        // Create new token with Sanctum
        $token = $user->createToken('auth-token');

        return [
            'user' => $user->load('roles', 'tenant', 'branch'),
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
            'expires_at' => null // Sanctum tokens don't expire by default
        ];
    }

    /**
     * Get user profile
     */
    public function getUserProfile(User $user): User
    {
        return $user->load('roles', 'permissions', 'tenant', 'branch');
    }

    /**
     * Update user profile
     */
    public function updateProfile(User $user, array $data): User
    {
        // Verify current password if new password is provided
        if (isset($data['password']) && isset($data['current_password'])) {
            if (!Hash::check($data['current_password'], $user->password)) {
                throw new \Exception('Current password is incorrect');
            }
            $data['password'] = Hash::make($data['password']);
        }

        // Remove current_password from update data
        unset($data['current_password']);
        unset($data['password_confirmation']);

        $user->update($data);
        
        return $user->fresh()->load('roles', 'permissions', 'tenant', 'branch');
    }

    /**
     * Change user password
     */
    public function changePassword(User $user, array $data): bool
    {
        if (!Hash::check($data['current_password'], $user->password)) {
            return false;
        }

        $user->update([
            'password' => Hash::make($data['password']),
            'remember_token' => Str::random(60),
        ]);

        // Optionally revoke all tokens except current
        // $user->tokens()->where('id', '!=', $user->token()->id)->delete();

        return true;
    }

    /**
     * Verify email address
     */
    public function verifyEmail(User $user): bool
    {
        if ($user->hasVerifiedEmail()) {
            return false;
        }

        $user->markEmailAsVerified();
        return true;
    }

    /**
     * Resend email verification
     */
    public function resendEmailVerification(User $user): bool
    {
        if ($user->hasVerifiedEmail()) {
            return false;
        }

        $user->sendEmailVerificationNotification();
        return true;
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission(User $user, string $permission): bool
    {
        return $user->can($permission);
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(User $user, string $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Assign role to user
     */
    public function assignRole(User $user, string $role): bool
    {
        try {
            $user->assignRole($role);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remove role from user
     */
    public function removeRole(User $user, string $role): bool
    {
        try {
            $user->removeRole($role);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get user's active tokens count
     */
    public function getActiveTokensCount(User $user): int
    {
        return $user->tokens()->count();
    }

    /**
     * Get user's login history (if you have a sessions table)
     */
    public function getLoginHistory(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $user->userSessions()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get user roles
     */
    public function getUserRoles(User $user): array
    {
        return $user->getRoleNames()->toArray();
    }

    /**
     * Get user permissions
     */
    public function getUserPermissions(User $user): array
    {
        return $user->getAllPermissions()->pluck('name')->toArray();
    }

    /**
     * Get user abilities for token (can be used with Sanctum abilities)
     * Note: Sanctum supports token abilities for fine-grained permissions
     * if you want to use role/permission-based token abilities
     */
    protected function getUserAbilities(User $user): array
    {
        $abilities = ['*']; // Default ability
        
        // Add role-based abilities
        $roles = $user->getRoleNames()->toArray();
        foreach ($roles as $role) {
            $abilities[] = 'role:' . $role;
        }
        
        // Add permission-based abilities
        $permissions = $user->getAllPermissions()->pluck('name')->toArray();
        foreach ($permissions as $permission) {
            $abilities[] = 'permission:' . $permission;
        }
        
        return array_unique($abilities);
    }

    /**
     * Generate API secret key
     */
    public function generateSecretKey(): string
    {
        return base64_encode(Str::random(32));
    }

    /**
     * Validate API secret key
     */
    public function validateSecretKey(string $key, string $hash): bool
    {
        return Hash::check($key, $hash);
    }
}
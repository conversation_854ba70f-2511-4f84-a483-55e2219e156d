@extends('layouts.dashboard')

@section('title', __('messages.welcome'))

@section('content')
<div class="flex flex-col items-center justify-center min-h-[60vh]">
    <h1 class="text-4xl font-bold mb-4">{{ __('messages.welcome') }}</h1>
    <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">{{ __('messages.welcome_message', ['app' => config('app.name', 'RestaurantPOS')]) }}</p>
    <a href="{{ route('dashboard') }}" class="px-6 py-3 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition">{{ __('messages.go_to_dashboard') }}</a>
</div>
@endsection

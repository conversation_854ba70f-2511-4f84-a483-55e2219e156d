@extends('layouts.dashboard')

@section('title', 'Dashboard Test')
@section('breadcrumb', 'Dashboard Test')

@section('content')
<div class="space-y-6">
    <!-- Test Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Card 1 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('messages.today_sales') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">$12,345</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-green-600 dark:text-green-400 text-sm font-medium">+12.5%</span>
                <span class="text-gray-600 dark:text-gray-400 text-sm">from yesterday</span>
            </div>
        </div>

        <!-- Card 2 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('messages.total_orders') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">156</p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-green-600 dark:text-green-400"></i>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-green-600 dark:text-green-400 text-sm font-medium">+8.2%</span>
                <span class="text-gray-600 dark:text-gray-400 text-sm">from yesterday</span>
            </div>
        </div>

        <!-- Card 3 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('messages.active_customers') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">89</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-red-600 dark:text-red-400 text-sm font-medium">-2.1%</span>
                <span class="text-gray-600 dark:text-gray-400 text-sm">from yesterday</span>
            </div>
        </div>

        <!-- Card 4 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('messages.staff_online') }}</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">12</p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-tie text-yellow-600 dark:text-yellow-400"></i>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-green-600 dark:text-green-400 text-sm font-medium">+5.4%</span>
                <span class="text-gray-600 dark:text-gray-400 text-sm">from yesterday</span>
            </div>
        </div>
    </div>

    <!-- Test Responsive Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Chart Area -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{{ __('messages.sales_trend') }}</h3>
            <div class="h-64 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <p class="text-gray-500 dark:text-gray-400">Chart placeholder</p>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{{ __('messages.recent_orders') }}</h3>
            <div class="space-y-3">
                @for($i = 1; $i <= 5; $i++)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 dark:text-blue-400 text-sm font-medium">#{{ $i }}</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Order #{{ 1000 + $i }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Table {{ $i }}</p>
                        </div>
                    </div>
                    <div class="text-right rtl:text-left">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${{ 25 + ($i * 5) }}</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Completed</p>
                    </div>
                </div>
                @endfor
            </div>
        </div>
    </div>

    <!-- Test Buttons and Components -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{{ __('messages.quick_actions') }}</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors">
                <i class="fas fa-plus text-blue-600 dark:text-blue-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400">{{ __('messages.add_new_order') }}</span>
            </button>
            
            <button class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors">
                <i class="fas fa-utensils text-green-600 dark:text-green-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-green-600 dark:text-green-400">{{ __('messages.manage_menus') }}</span>
            </button>
            
            <button class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors">
                <i class="fas fa-chart-bar text-purple-600 dark:text-purple-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-purple-600 dark:text-purple-400">{{ __('messages.view_reports') }}</span>
            </button>
            
            <button class="flex flex-col items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-lg transition-colors">
                <i class="fas fa-cog text-yellow-600 dark:text-yellow-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-yellow-600 dark:text-yellow-400">{{ __('messages.update_settings') }}</span>
            </button>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Test specific styles */
    .test-responsive {
        transition: all 0.3s ease;
    }
    
    @media (max-width: 640px) {
        .test-responsive {
            font-size: 0.875rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    // Test JavaScript functionality
    console.log('Dashboard test page loaded successfully');
    
    // Test responsive behavior
    window.addEventListener('resize', () => {
        console.log('Window resized to:', window.innerWidth, 'x', window.innerHeight);
    });
</script>
@endpush


* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-bg: #1f2937;
            --darker-bg: #111827;
            --light-bg: #f9fafb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-light: #ffffff;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        body {
            display: flex;
            min-height: 100vh;
            position: relative;
        }
        
        .content-container {
            padding: 20px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
            color: var(--text-light);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: hidden;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none;  /* IE and Edge */
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .sidebar::-webkit-scrollbar {
            display: none;
            box-shadow: var(--shadow-lg);
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar.rtl {
            right: 0;
            left: auto;
        }

        .sidebar.rtl.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-light);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            padding-left: 30px;
        }

        .nav-link.active {
            background: var(--primary-color);
            border-right: 3px solid var(--accent-color);
        }

        .nav-link.rtl.active {
            border-right: none;
            border-left: 3px solid var(--accent-color);
        }

        .nav-link.rtl:hover {
            padding-left: 20px;
            padding-right: 30px;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            fill: currentColor;
        }

        .nav-icon.rtl {
            margin-right: 0;
            margin-left: 12px;
        }

        .nav-text {
            flex: 1;
        }

        .nav-arrow {
            width: 12px;
            height: 12px;
            transition: transform 0.3s ease;
        }

        .nav-arrow.expanded {
            transform: rotate(180deg);
        }

        .nav-arrow.rtl {
            transform: rotate(180deg);
        }

        .nav-arrow.rtl.expanded {
            transform: rotate(0deg);
        }

        .sub-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.3);
        }

        .sub-menu.expanded {
            max-height: 300px;
        }

        .sub-nav-link {
            display: flex;
            align-items: center;
            padding: 10px 20px 10px 52px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sub-nav-link.rtl {
            padding: 10px 52px 10px 20px;
        }

        .sub-nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            display: flex;
            flex-direction: column;
            width: calc(100% - 280px);
        }

        .main-content.expanded {
            margin-left: 0;
            width: 100%;
        }

        .main-content.rtl {
            margin-left: 0;
            margin-right: 280px;
            width: calc(100% - 280px);
        }

        .main-content.rtl.expanded {
            margin-right: 0;
            width: 100%;
        }

        /* Navbar */
        .navbar {
            background: white;
            padding: 15px 20px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 900;
            width: 100%;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: var(--light-bg);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .navbar-item {
            position: relative;
        }

        .navbar-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .navbar-btn:hover {
            background: var(--light-bg);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notification-badge.rtl {
            right: auto;
            left: -5px;
        }

        .profile-img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Dropdown */
        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid var(--border-color);
        }

        .dropdown.rtl {
            right: auto;
            left: 0;
        }

        .dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-primary);
            text-decoration: none;
            transition: background 0.3s ease;
            border-bottom: 1px solid var(--border-color);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: var(--light-bg);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--border-color);
            margin: 5px 0;
        }

        /* Content Area */
        .content {
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease;
            width: 100%;
        }
        
        .card .table {
            width: 100%;
        }
        
        .dataTables_wrapper {
            width: 100%;
            overflow-x: auto;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .stat-icon.primary {
            background: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }

        .stat-icon.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .stat-content h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-content p {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        /* RTL Styles */
        .rtl {
            direction: rtl;
        }

        .rtl .navbar-left {
            order: 2;
        }

        .rtl .navbar-right {
            order: 1;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(-100%);
            }

            .sidebar.rtl {
                transform: translateX(100%);
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.rtl {
                margin-right: 0;
            }

            .navbar {
                padding: 10px 15px;
            }

            .navbar-right {
                gap: 10px;
            }

            .content {
                padding: 15px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .breadcrumb {
                display: none;
            }

            .dropdown {
                position: fixed;
                top: 60px;
                right: 10px;
                left: auto;
                width: calc(100% - 20px);
                max-width: 300px;
            }

            .dropdown.rtl {
                right: auto;
                left: 10px;
            }
        }

        /* Overlay for mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Animation keyframes */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeIn 0.5s ease;
        }

        /* Language switcher */
        .language-switcher {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            font-weight: 500;
        }

        .flag-icon {
            width: 20px;
            height: 15px;
            border-radius: 2px;
            background-size: cover;
            background-position: center;
        }

        .flag-icon.en {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30"><rect fill="%23012169" width="60" height="30"/><path d="M0 0l60 30M60 0L0 30" stroke="%23fff" stroke-width="6"/><path d="M0 0l60 30M60 0L0 30" stroke="%23c8102e" stroke-width="4"/><path d="M30 0v30M0 15h60" stroke="%23fff" stroke-width="10"/><path d="M30 0v30M0 15h60" stroke="%23c8102e" stroke-width="6"/></svg>');
        }

        .flag-icon.ar {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30"><rect fill="%23000" width="60" height="10"/><rect fill="%23fff" y="10" width="60" height="10"/><rect fill="%23ce1126" y="20" width="60" height="10"/><path d="M0 0h30v30H0z" fill="%23ce1126"/><path d="M15 7.5l2.5 7.5h8l-6.5 4.5 2.5 7.5L15 22.5 8.5 27l2.5-7.5L4.5 15h8L15 7.5z" fill="%23fff"/></svg>');
        }

        /* Status indicators */
        .status-online {
            color: var(--success-color);
        }

        .status-offline {
            color: var(--text-secondary);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            display: inline-block;
            margin-right: 5px;
        }

        .status-indicator.rtl {
            margin-right: 0;
            margin-left: 5px;
        }
  
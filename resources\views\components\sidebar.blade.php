<!-- Sidebar Component -->
<aside id="sidebar" class="sidebar {{ app()->getLocale() === 'ar' ? 'sidebar-right' : 'sidebar-left' }}">
    <!-- Sidebar Header -->
    <div class="sidebar-header p-4 border-b border-gray-700 dark:border-gray-600">
        <div class="flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-utensils text-white text-sm"></i>
                </div>
                <div class="sidebar-text">
                    <h1 class="text-white font-bold text-lg">{{ config('app.name', 'EPSIS') }}</h1>
                    <p class="text-gray-400 text-xs">{{ __('messages.restaurant_pos') }}</p>
                </div>
            </div>
            <!-- Collapse <PERSON><PERSON> (Desktop) -->
            <button id="sidebar-collapse" class="hidden md:block text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-chevron-left sidebar-collapse-icon"></i>
            </button>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav flex-1 overflow-y-auto py-4">
        <ul class="space-y-1 px-3">
            <!-- Dashboard -->
            <li class="nav-item">
                <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <span class="nav-text">{{ __('messages.dashboard') }}</span>
                </a>
            </li>

            <!-- Menus Section -->
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="menus">
                    <i class="fas fa-utensils nav-icon"></i>
                    <span class="nav-text">{{ __('messages.menus') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('menus.index') }}" class="submenu-link">{{ __('messages.menus') }}</a></li>
                    <li><a href="{{ route('menu-items.index') }}" class="submenu-link">{{ __('messages.menu_items') }}</a></li>
                    <li><a href="{{ route('item-categories.index') }}" class="submenu-link">{{ __('messages.item_categories') }}</a></li>
                    <li><a href="{{ route('item-variations.index') }}" class="submenu-link">{{ __('messages.item_variations') }}</a></li>
                    <li><a href="{{ route('item-addons.index') }}" class="submenu-link">{{ __('messages.item_addons') }}</a></li> --}}
                </ul>
            </li>

             {{-- Tables Section --}}
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="tables">
                    <i class="fas fa-table nav-icon"></i>
                    <span class="nav-text">{{ __('messages.tables') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('areas.index') }}" class="submenu-link">{{ __('messages.areas') }}</a></li>
                    <li><a href="{{ route('tables.index') }}" class="submenu-link">{{ __('messages.tables') }}</a></li>
                    <li><a href="{{ route('qr-codes.index') }}" class="submenu-link">{{ __('messages.qr_codes') }}</a></li>
                    <li><a href="{{ route('waiter-requests.index') }}" class="submenu-link">{{ __('messages.waiter_requests') }}</a></li>
                    <li><a href="{{ route('reservations.index') }}" class="submenu-link">{{ __('messages.reservations') }}</a></li> --}}
                </ul>
            </li>

            <!-- POS -->
            <li class="nav-item">
                <a href="" class="nav-link ">
                {{-- <a href="{{ route('pos.index') }}" class="nav-link {{ request()->routeIs('pos.*') ? 'active' : '' }}"> --}}
                    <i class="fas fa-cash-register nav-icon"></i>
                    <span class="nav-text">{{ __('messages.pos') }}</span>
                </a>
            </li>

            <!-- Orders -->
            <li class="nav-item">
                {{-- <a href="{{ route('orders.index') }}" class="nav-link {{ request()->routeIs('orders.*') ? 'active' : '' }}">
                    <i class="fas fa-shopping-cart nav-icon"></i>
                    <span class="nav-text">{{ __('messages.orders') }}</span>
                    @if(isset($pendingOrdersCount) && $pendingOrdersCount > 0)
                        <span class="badge bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-auto rtl:ml-0 rtl:mr-auto">{{ $pendingOrdersCount }}</span>
                    @endif
                </a> --}}
            </li>

            <!-- Customers -->
            <li class="nav-item">
                {{-- <a href="{{ route('customers.index') }}" class="nav-link {{ request()->routeIs('customers.*') ? 'active' : '' }}">
                    <i class="fas fa-users nav-icon"></i>
                    <span class="nav-text">{{ __('messages.customers') }}</span>
                </a> --}}
            </li>

            <!-- Staff -->
            <li class="nav-item">
                {{-- <a href="{{ route('staff.index') }}" class="nav-link {{ request()->routeIs('staff.*') ? 'active' : '' }}">
                    <i class="fas fa-user-tie nav-icon"></i>
                    <span class="nav-text">{{ __('messages.staff') }}</span>
                </a> --}}
            </li>

            <!-- Delivery Executive -->
            <li class="nav-item">
                {{-- <a href="{{ route('delivery-executives.index') }}" class="nav-link {{ request()->routeIs('delivery-executives.*') ? 'active' : '' }}">
                    <i class="fas fa-motorcycle nav-icon"></i>
                    <span class="nav-text">{{ __('messages.delivery_executive') }}</span>
                </a> --}}
            </li>

            <!-- Payments Section -->
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="payments">
                    <i class="fas fa-credit-card nav-icon"></i>
                    <span class="nav-text">{{ __('messages.payments') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('payments.index') }}" class="submenu-link">{{ __('messages.payments') }}</a></li>
                    <li><a href="{{ route('due-payments.index') }}" class="submenu-link">{{ __('messages.due_payments') }}</a></li> --}}
                </ul>
            </li>

            <!-- Expenses Section -->
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="expenses">
                    <i class="fas fa-receipt nav-icon"></i>
                    <span class="nav-text">{{ __('messages.expenses') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('expenses.index') }}" class="submenu-link">{{ __('messages.expenses') }}</a></li>
                    <li><a href="{{ route('expense-categories.index') }}" class="submenu-link">{{ __('messages.expense_categories') }}</a></li> --}}
                </ul>
            </li>

            <!-- Reports Section -->
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="reports">
                    <i class="fas fa-chart-bar nav-icon"></i>
                    <span class="nav-text">{{ __('messages.reports') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('reports.sales') }}" class="submenu-link">{{ __('messages.sales_report') }}</a></li>
                    <li><a href="{{ route('reports.items') }}" class="submenu-link">{{ __('messages.item_report') }}</a></li>
                    <li><a href="{{ route('reports.categories') }}" class="submenu-link">{{ __('messages.category_report') }}</a></li>
                    <li><a href="{{ route('reports.expenses') }}" class="submenu-link">{{ __('messages.expense_reports') }}</a></li> --}}
                </ul>
            </li>

            <!-- Inventory Section -->
            <li class="nav-item">
                <button class="nav-link submenu-toggle w-full text-left" data-submenu="inventory">
                    <i class="fas fa-boxes nav-icon"></i>
                    <span class="nav-text">{{ __('messages.inventory') }}</span>
                    <i class="fas fa-chevron-right submenu-icon ml-auto rtl:ml-0 rtl:mr-auto transition-transform"></i>
                </button>
                <ul class="submenu hidden ml-6 rtl:ml-0 rtl:mr-6 mt-2 space-y-1">
                    {{-- <li><a href="{{ route('inventory.dashboard') }}" class="submenu-link">{{ __('messages.inventory_dashboard') }}</a></li>
                    <li><a href="{{ route('inventory.units.index') }}" class="submenu-link">{{ __('messages.units') }}</a></li>
                    <li><a href="{{ route('inventory.items.index') }}" class="submenu-link">{{ __('messages.inventory_items') }}</a></li>
                    <li><a href="{{ route('inventory.categories.index') }}" class="submenu-link">{{ __('messages.inventory_item_categories') }}</a></li>
                    <li><a href="{{ route('inventory.stocks.index') }}" class="submenu-link">{{ __('messages.inventory_stocks') }}</a></li>
                    <li><a href="{{ route('inventory.movements.index') }}" class="submenu-link">{{ __('messages.inventory_movements') }}</a></li>
                    <li><a href="{{ route('inventory.recipes.index') }}" class="submenu-link">{{ __('messages.recipes') }}</a></li>
                    <li><a href="{{ route('inventory.purchase-orders.index') }}" class="submenu-link">{{ __('messages.purchase_orders') }}</a></li>
                    <li><a href="{{ route('inventory.suppliers.index') }}" class="submenu-link">{{ __('messages.suppliers') }}</a></li>
                    <li><a href="{{ route('inventory.reports.index') }}" class="submenu-link">{{ __('messages.inventory_reports') }}</a></li> --}}
                </ul>
            </li>

            <!-- Settings -->
            <li class="nav-item">
                {{-- <a href="{{ route('settings.index') }}" class="nav-link {{ request()->routeIs('settings.*') ? 'active' : '' }}">
                    <i class="fas fa-cog nav-icon"></i>
                    <span class="nav-text">{{ __('messages.settings') }}</span>
                </a> --}}
            </li>

            <!-- Kitchens -->
            <li class="nav-item">
                {{-- <a href="{{ route('kitchens.index') }}" class="nav-link {{ request()->routeIs('kitchens.*') ? 'active' : '' }}">
                    <i class="fas fa-fire nav-icon"></i>
                    <span class="nav-text">{{ __('messages.kitchens') }}</span>
                </a> --}}
            </li>
        </ul>

        <!-- User Section (Bottom) -->
        <div class="sidebar-footer p-4 border-t border-gray-700 dark:border-gray-600 mt-auto">
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-gray-300 text-sm"></i>
                </div>
                <div class="sidebar-text flex-1 min-w-0">
                    <p class="text-white text-sm font-medium truncate">{{ auth()->user()->name ?? 'User' }}</p>
                    <p class="text-gray-400 text-xs truncate">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                </div>
            </div>
        </div> 
    </nav>
</aside>

<style>
/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width, 280px);
    height: 100vh;
    position: fixed;
    top: 0;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-left {
    left: 0;
}

.sidebar-right {
    right: 0;
}

/* Collapsed State */
.sidebar.collapsed {
    width: var(--sidebar-collapsed-width, 70px);
}

.sidebar.collapsed .sidebar-text,
.sidebar.collapsed .nav-text,
.sidebar.collapsed .submenu,
.sidebar.collapsed .submenu-icon {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .sidebar-collapse-icon {
    transform: rotate(180deg);
}

/* Navigation Links */
.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #cbd5e1;
    text-decoration: none;
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    transform: translateX(4px);
}

[dir="rtl"] .nav-link:hover {
    transform: translateX(-4px);
}

.nav-link.active {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
    font-size: 16px;
}

[dir="rtl"] .nav-icon {
    margin-right: 0;
    margin-left: 12px;
}

/* Submenu Styles */
.submenu-link {
    display: block;
    padding: 8px 16px;
    color: #94a3b8;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
}

.submenu-link:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding-left: 20px;
}

[dir="rtl"] .submenu-link:hover {
    padding-left: 16px;
    padding-right: 20px;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
}

/* Scrollbar */
.sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.5);
}
</style>

<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Web\MenuWebController;
use Modules\Menu\Http\Controllers\Web\CategoryWebController;
use Modules\Menu\Http\Controllers\Web\MenuItemWebController;
use Modules\Menu\Http\Controllers\Web\AddonWebController;
use Modules\Menu\Http\Controllers\Web\VariantWebController;
use Modules\Menu\Http\Middleware\MenuAccessMiddleware;

Route::middleware(['auth', MenuAccessMiddleware::class])->prefix('menu')->group(function () {
    Route::resource('menus', MenuWebController::class);
    Route::resource('menu-items', MenuItemWebController::class);
    Route::resource('categories', CategoryWebController::class);
    Route::resource('variants', VariantWebController::class);
    Route::resource('addons', AddonWebController::class);
});


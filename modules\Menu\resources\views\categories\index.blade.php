@extends('layouts.app')

@section('title', 'Menu Categories')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4>Menu Categories</h4>
        <button class="btn btn-primary" onclick="openModal('createCategoryModal', 'Create Category')">Create New</button>
    </div>
    <div class="card-body">
        <table id="categoriesTable" class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCategoryForm" action="{{ route('categories.store') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Form loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        const table = initDataTable('categoriesTable', '{{ route('categories.index') }}', [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'description', name: 'description' },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false 
            }
        ]);

        // Handle create form submission
        submitForm('createCategoryForm', function(data) {
            $('#createCategoryModal').modal('hide');
            table.ajax.reload();
            Toast.success('Category created successfully');
        }, function(errors) {
            console.log(errors);
        });

        // Handle edit button clicks
        $(document).on('click', '.edit-category', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('categories.show', ':id') }}'.replace(':id', id);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const category = data.data;
                        $('#editCategoryModal .modal-body').html(`
                            <form id="editCategoryForm" action="{{ route('categories.update', ':id') }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label class="form-label">Name</label>
                                    <input type="text" name="name" class="form-control" value="${category.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea name="description" class="form-control">${category.description || ''}</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Update</button>
                            </form>
                        `.replace(':id', id));
                        
                        $('#editCategoryModal').modal('show');
                        
                        // Handle edit form submission
                        submitForm('editCategoryForm', function(data) {
                            $('#editCategoryModal').modal('hide');
                            table.ajax.reload();
                            Toast.success('Category updated successfully');
                        }, function(errors) {
                            console.log(errors);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('Failed to load category data');
                });
        });

        // Handle delete button clicks
        $(document).on('click', '.delete-category', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('categories.destroy', ':id') }}'.replace(':id', id);
            
            confirmDelete(url, function() {
                table.ajax.reload();
            });
        });
    });
</script>
@endpush
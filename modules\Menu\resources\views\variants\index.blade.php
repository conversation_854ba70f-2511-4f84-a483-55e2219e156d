@extends('layouts.app')

@section('title', 'Menu Variants')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4>Menu Variants</h4>
        <button class="btn btn-primary" onclick="openModal('createVariantModal', 'Create Variant')">Create New</button>
    </div>
    <div class="card-body">
        <table id="variantsTable" class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Menu Item</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createVariantModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Variant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createVariantForm" action="{{ route('variants.store') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Price Modifier</label>
                        <input type="number" step="0.01" name="price_modifier" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Menu Item</label>
                        <select name="menu_item_id" class="form-control" required>
                            <!-- Options loaded dynamically -->
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editVariantModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Variant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Form loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        const table = initDataTable('variantsTable', '{{ route('variants.index') }}', [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'price_modifier', name: 'price_modifier' },
            { data: 'menu_item_name', name: 'menu_item_name' },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false 
            }
        ]);

        // Load menu items for create form
        loadMenuItems();

        // Handle create form submission
        submitForm('createVariantForm', function(data) {
            $('#createVariantModal').modal('hide');
            table.ajax.reload();
            Toast.success('Variant created successfully');
        }, function(errors) {
            console.log(errors);
        });

        // Handle edit button clicks
        $(document).on('click', '.edit-variant', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('variants.show', ':id') }}'.replace(':id', id);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const variant = data.data;
                        $('#editVariantModal .modal-body').html(`
                            <form id="editVariantForm" action="{{ route('variants.update', ':id') }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label class="form-label">Name</label>
                                    <input type="text" name="name" class="form-control" value="${variant.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Price Modifier</label>
                                    <input type="number" step="0.01" name="price_modifier" class="form-control" value="${variant.price_modifier}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Menu Item</label>
                                    <select name="menu_item_id" class="form-control" required>
                                        <option value="${variant.menu_item_id}" selected>${variant.menu_item_name}</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">Update</button>
                            </form>
                        `.replace(':id', id));
                        
                        // Load menu items for edit form
                        loadMenuItems('#editVariantForm select[name="menu_item_id"]', variant.menu_item_id);
                        
                        $('#editVariantModal').modal('show');
                        
                        // Handle edit form submission
                        submitForm('editVariantForm', function(data) {
                            $('#editVariantModal').modal('hide');
                            table.ajax.reload();
                            Toast.success('Variant updated successfully');
                        }, function(errors) {
                            console.log(errors);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('Failed to load variant data');
                });
        });

        // Handle delete button clicks
        $(document).on('click', '.delete-variant', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('variants.destroy', ':id') }}'.replace(':id', id);
            
            confirmDelete(url, function() {
                table.ajax.reload();
            });
        });

        // Function to load menu items
        function loadMenuItems(selector = '#createVariantForm select[name="menu_item_id"]', selectedId = null) {
            fetch('{{ route('menu-items.index') }}?all=true')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = $(selector);
                        select.empty();
                        select.append('<option value="">Select Menu Item</option>');
                        
                        data.data.forEach(item => {
                            const selected = selectedId && selectedId == item.id ? 'selected' : '';
                            select.append(`<option value="${item.id}" ${selected}>${item.name}</option>`);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading menu items:', error);
                });
        }
    });
</script>
@endpush
<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\Menu\Services\MenuService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;

class MenuItemWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $menuItems = MenuItem::with(['category', 'menu']);
            
            // Apply filters
            if ($request->filled('menu_id')) {
                $menuItems->where('menu_id', $request->menu_id);
            }
            
            if ($request->filled('category_id')) {
                $menuItems->where('category_id', $request->category_id);
            }
            
            if ($request->filled('is_active')) {
                $menuItems->where('is_active', $request->is_active);
            }
            
            if ($request->filled('is_featured')) {
                $menuItems->where('is_featured', $request->is_featured);
            }
            
            if ($request->filled('is_spicy')) {
                $menuItems->where('is_spicy', $request->is_spicy);
            }
            
            return DataTables::of($menuItems)
                ->addColumn('menu_name', function ($menuItem) {
                    return $menuItem->menu ? $menuItem->menu->name : 'N/A';
                })
                ->addColumn('category_name', function ($menuItem) {
                    return $menuItem->category ? $menuItem->category->name : 'N/A';
                })
                ->addColumn('base_price_formatted', function ($menuItem) {
                    return '$' . number_format($menuItem->base_price, 2);
                })
                ->addColumn('cost_price_formatted', function ($menuItem) {
                    return $menuItem->cost_price ? '$' . number_format($menuItem->cost_price, 2) : 'N/A';
                })
                ->addColumn('prep_time_formatted', function ($menuItem) {
                    return $menuItem->prep_time_minutes ? $menuItem->prep_time_minutes . ' min' : 'N/A';
                })
                ->addColumn('status_badge', function ($menuItem) {
                    return $menuItem->is_active 
                        ? '<span class="badge bg-success">Active</span>' 
                        : '<span class="badge bg-danger">Inactive</span>';
                })
                ->addColumn('featured_badge', function ($menuItem) {
                    return $menuItem->is_featured 
                        ? '<span class="badge bg-warning">Featured</span>' 
                        : '';
                })
                ->addColumn('spicy_badge', function ($menuItem) {
                    if ($menuItem->is_spicy) {
                        $level = $menuItem->spice_level ? ' (Level ' . $menuItem->spice_level . ')' : '';
                        return '<span class="badge bg-danger">🌶️ Spicy' . $level . '</span>';
                    }
                    return '';
                })
                ->addColumn('dietary_info_badges', function ($menuItem) {
                    $badges = '';
                    if ($menuItem->dietary_info && is_array($menuItem->dietary_info)) {
                        foreach ($menuItem->dietary_info as $info) {
                            $badges .= '<span class="badge bg-info me-1">' . ucfirst($info) . '</span>';
                        }
                    }
                    return $badges;
                })
                ->addColumn('actions', function ($menuItem) {
                    return '
                        <button type="button" class="btn btn-sm btn-primary edit-menu-item" 
                                data-id="' . $menuItem->id . '" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-info view-menu-item" 
                                data-id="' . $menuItem->id . '" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-menu-item" 
                                data-id="' . $menuItem->id . '" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    ';
                })
                ->rawColumns(['status_badge', 'featured_badge', 'spicy_badge', 'dietary_info_badges', 'actions'])
                ->make(true);
        }

        // Get menus and categories for filters
        $menus = \App\Models\Menu::select('id', 'name')->where('is_active', true)->orderBy('name')->get();
        $categories = \App\Models\MenuCategory::select('id', 'name')->where('is_active', true)->orderBy('name')->get();

        return view('menu::menu-items.index', compact('menus', 'categories'));
    }

    public function store(StoreMenuItemRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $menuItem = $this->menuService->createMenuItem($data);
            
            return response()->json([
                'success' => true,
                'message' => 'Menu item created successfully',
                'data' => $menuItem
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(string $id): JsonResponse
    {
        try {
            $menuItem = $this->menuService->getMenuItemById($id);
            return response()->json([
                'success' => true,
                'data' => $menuItem
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Menu item not found'
            ], 404);
        }
    }

    public function update(UpdateMenuItemRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $menuItem = $this->menuService->updateMenuItem($id, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Menu item updated successfully',
                'data' => $menuItem
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(string $id): JsonResponse
    {
        try {
            $this->menuService->deleteMenuItem($id);
            return response()->json([
                'success' => true,
                'message' => 'Menu item deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting menu item: ' . $e->getMessage()
            ], 500);
        }
    }
}
    
let currentLanguage = 'en';
        let sidebarCollapsed = false;
        
        // Language translations
        const translations = {
            en: {
                dashboard: 'Dashboard',
                orders: 'Orders',
                menu: 'Menu',
                inventory: 'Inventory',
                reports: 'Reports',
                settings: 'Settings',
                newOrder: 'New Order',
                orderHistory: 'Order History',
                pendingOrders: 'Pending Orders',
                foodItems: 'Food Items',
                beverages: 'Beverages',
                categories: 'Categories',
                todaysOrders: "Today's Orders",
                todaysRevenue: "Today's Revenue",
                averageRating: 'Average Rating',
                lowStockItems: 'Low Stock Items',
                salesOverview: 'Sales Overview',
                popularItems: 'Popular Items',
                orderStatus: 'Order Status',
                profile: 'Profile',
                logout: 'Logout',
                newOrderReceived: 'New order received',
                lowStockAlert: 'Low stock alert',
                dailySalesReport: 'Daily sales report'
            },
            ar: {
                dashboard: 'لوحة التحكم',
                orders: 'الطلبات',
                menu: 'القائمة',
                inventory: 'المخزون',
                reports: 'التقارير',
                settings: 'الإعدادات',
                newOrder: 'طلب جديد',
                orderHistory: 'تاريخ الطلبات',
                pendingOrders: 'الطلبات المعلقة',
                foodItems: 'المأكولات',
                beverages: 'المشروبات',
                categories: 'الفئات',
                todaysOrders: 'طلبات اليوم',
                todaysRevenue: 'إيرادات اليوم',
                averageRating: 'متوسط التقييم',
                lowStockItems: 'عناصر المخزون المنخفض',
                salesOverview: 'نظرة عامة على المبيعات',
                popularItems: 'العناصر الشائعة',
                orderStatus: 'حالة الطلب',
                profile: 'الملف الشخصي',
                logout: 'تسجيل الخروج',
                newOrderReceived: 'تم استلام طلب جديد',
                lowStockAlert: 'تنبيه مخزون منخفض',
                dailySalesReport: 'تقرير المبيعات اليومي'
            }
        };
        
        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (window.innerWidth <= 768) {
                // Mobile behavior
                sidebar.classList.toggle('collapsed');
                overlay.classList.toggle('show');
            } else {
                // Desktop behavior
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
            
            // Ensure DataTables adjust their width
            setTimeout(function() {
                if ($.fn.dataTable) {
                    $('.dataTable').DataTable().columns.adjust();
                }
            }, 300);
        }
        
        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay').addEventListener('click', function() {
            toggleSidebar();
        });
        
        // Toggle dropdown
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const allDropdowns = document.querySelectorAll('.dropdown');
            
            // Close all other dropdowns
            allDropdowns.forEach(d => {
                if (d.id !== dropdownId) {
                    d.classList.remove('show');
                }
            });
            
            dropdown.classList.toggle('show');
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const dropdowns = document.querySelectorAll('.dropdown');
            const buttons = document.querySelectorAll('.navbar-btn');
            
            let clickedButton = false;
            buttons.forEach(button => {
                if (button.contains(event.target)) {
                    clickedButton = true;
                }
            });
            
            if (!clickedButton) {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
        
        // Toggle submenu
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.nav-arrow');
            
            submenu.classList.toggle('expanded');
            arrow.classList.toggle('expanded');
        }
        
        // Set active navigation
        function setActiveNav(element) {
            const navLinks = document.querySelectorAll('.nav-link, .sub-nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });
            element.classList.add('active');
            
            // Close sidebar on mobile after selection
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        }
        
        // Change language
        function changeLanguage(lang, closeDropdown = false) {
            currentLanguage = lang;
            const html = document.documentElement;
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const currentFlag = document.getElementById('currentFlag');
            const currentLangText = document.getElementById('currentLang');
            
            // Update direction
            if (lang === 'ar') {
                html.setAttribute('dir', 'rtl');
                html.classList.add('rtl');
                sidebar.classList.add('rtl');
                mainContent.classList.add('rtl');
                currentFlag.className = 'flag-icon ar';
                currentLangText.textContent = 'العربية';
            } else {
                html.setAttribute('dir', 'ltr');
                html.classList.remove('rtl');
                sidebar.classList.remove('rtl');
                mainContent.classList.remove('rtl');
                currentFlag.className = 'flag-icon en';
                currentLangText.textContent = 'EN';
            }
            
            // Update RTL classes for specific elements
            const navIcons = document.querySelectorAll('.nav-icon');
            const navArrows = document.querySelectorAll('.nav-arrow');
            const subNavLinks = document.querySelectorAll('.sub-nav-link');
            const navLinks = document.querySelectorAll('.nav-link');
            const notificationBadges = document.querySelectorAll('.notification-badge');
            const dropdowns = document.querySelectorAll('.dropdown');
            const statusIndicators = document.querySelectorAll('.status-indicator');
            
            [navIcons, navArrows, subNavLinks, navLinks, notificationBadges, dropdowns, statusIndicators].forEach(elements => {
                elements.forEach(element => {
                    if (lang === 'ar') {
                        element.classList.add('rtl');
                    } else {
                        element.classList.remove('rtl');
                    }
                });
            });
            
            // Update text content
            updateTextContent(lang);
            
            // Only close dropdown if requested (i.e., user action)
            if (closeDropdown) {
                toggleDropdown('languageDropdown');
            }
        }
        
        // Update text content based on language
        function updateTextContent(lang) {
            const t = translations[lang];
            
            // Update navigation items
            const navItems = {
                'Dashboard': t.dashboard,
                'Orders': t.orders,
                'Menu': t.menu,
                'Inventory': t.inventory,
                'Reports': t.reports,
                'Settings': t.settings
            };
            
            document.querySelectorAll('.nav-text').forEach(element => {
                const currentText = element.textContent.trim();
                if (navItems[currentText]) {
                    element.textContent = navItems[currentText];
                }
            });
            
            // Update submenu items
            const subNavItems = {
                'New Order': t.newOrder,
                'Order History': t.orderHistory,
                'Pending Orders': t.pendingOrders,
                'Food Items': t.foodItems,
                'Beverages': t.beverages,
                'Categories': t.categories
            };
            
            document.querySelectorAll('.sub-nav-link').forEach(element => {
                const currentText = element.textContent.trim();
                if (subNavItems[currentText]) {
                    element.textContent = subNavItems[currentText];
                }
            });
            
            // Update stats
            document.querySelectorAll('.stat-content p').forEach(element => {
                const currentText = element.textContent.trim();
                if (currentText === "Today's Orders") element.textContent = t.todaysOrders;
                else if (currentText === "Today's Revenue") element.textContent = t.todaysRevenue;
                else if (currentText === "Average Rating") element.textContent = t.averageRating;
                else if (currentText === "Low Stock Items") element.textContent = t.lowStockItems;
                else if (currentText === "طلبات اليوم") element.textContent = t.todaysOrders;
                else if (currentText === "إيرادات اليوم") element.textContent = t.todaysRevenue;
                else if (currentText === "متوسط التقييم") element.textContent = t.averageRating;
                else if (currentText === "عناصر المخزون المنخفض") element.textContent = t.lowStockItems;
            });
            
            // Update chart titles
            document.querySelectorAll('.chart-title').forEach(element => {
                const currentText = element.textContent.trim();
                if (currentText === "Sales Overview") element.textContent = t.salesOverview;
                else if (currentText === "Popular Items") element.textContent = t.popularItems;
                else if (currentText === "Order Status") element.textContent = t.orderStatus;
                else if (currentText === "نظرة عامة على المبيعات") element.textContent = t.salesOverview;
                else if (currentText === "العناصر الشائعة") element.textContent = t.popularItems;
                else if (currentText === "حالة الطلب") element.textContent = t.orderStatus;
            });
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');
            
            if (window.innerWidth > 768) {
                overlay.classList.remove('show');
                if (!sidebarCollapsed) {
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('expanded');
                }
            } else {
                if (!sidebarCollapsed) {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('expanded');
                }
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial language
            changeLanguage('en');
            
            // Handle mobile sidebar
            if (window.innerWidth <= 768) {
                sidebarCollapsed = true;
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }
        });
        
        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                // Prevent default behavior only if href is just '#'
                if (this.getAttribute('href') === '#') {
                    e.preventDefault();
                }
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            // Toggle sidebar with Ctrl+B
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                toggleSidebar();
            }
            
            // Close dropdowns with Escape
            if (e.key === 'Escape') {
                document.querySelectorAll('.dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
  
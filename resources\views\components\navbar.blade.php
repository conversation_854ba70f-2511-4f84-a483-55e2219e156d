<!-- Navbar Component -->
<header class="navbar bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
    <div class="flex items-center justify-between h-16 px-4 lg:px-6">
        <!-- Left Section -->
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <!-- Mobile Menu Toggle -->
            <button id="sidebar-toggle" class="md:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                <i class="fas fa-bars text-lg"></i>
            </button>

            <!-- Desktop Sidebar Toggle -->
            <button id="sidebar-toggle" class="hidden md:block p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                <i class="fas fa-bars text-lg"></i>
            </button>

            <!-- Breadcrumb -->
            <nav class="hidden sm:flex items-center space-x-2 rtl:space-x-reverse text-sm">
                <a href="{{ route('dashboard') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
                    <i class="fas fa-home"></i>
                </a>
                @if(isset($breadcrumbs) && count($breadcrumbs) > 0)
                    @foreach($breadcrumbs as $breadcrumb)
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                        @if($loop->last)
                            <span class="text-gray-900 dark:text-gray-100 font-medium">{{ $breadcrumb['title'] }}</span>
                        @else
                            <a href="{{ $breadcrumb['url'] }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
                                {{ $breadcrumb['title'] }}
                            </a>
                        @endif
                    @endforeach
                @else
                    <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    <span class="text-gray-900 dark:text-gray-100 font-medium">@yield('breadcrumb', __('messages.dashboard'))</span>
                @endif
            </nav>
        </div>

        <!-- Right Section -->
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <!-- Search -->
            <div class="hidden lg:block relative">
                <div class="relative">
                    <input type="text" id="global-search" placeholder="{{ __('messages.search_placeholder') }}" 
                           class="w-64 pl-10 rtl:pl-4 rtl:pr-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                    <i class="fas fa-search absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <!-- Search Results Dropdown -->
                <div id="search-results" class="absolute top-full left-0 rtl:left-auto rtl:right-0 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hidden z-50 max-h-96 overflow-y-auto">
                    <!-- Search results will be populated here -->
                </div>
            </div>

            <!-- Language Switcher -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                    @if(app()->getLocale() === 'ar')
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiMwMDczM0UiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjEwIiB3aWR0aD0iMjAiIGhlaWdodD0iNSIgZmlsbD0iIzAwNzMzRSIvPgo8L3N2Zz4K" alt="Arabic" class="w-5 h-4">
                        <span class="hidden sm:inline text-sm font-medium">العربية</span>
                    @else
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+CjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSIyIiB3aWR0aD0iMjAiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPC9zdmc+" alt="English" class="w-5 h-4">
                        <span class="hidden sm:inline text-sm font-medium">English</span>
                    @endif
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                
                {{-- <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-50">
                    <a href="{{ route('language.switch', 'en') }}" class="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors {{ app()->getLocale() === 'en' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : '' }}">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+CjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSIyIiB3aWR0aD0iMjAiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPC9zdmc+" alt="English" class="w-5 h-4">
                        <span>English</span>
                        @if(app()->getLocale() === 'en')
                            <i class="fas fa-check text-blue-500 ml-auto rtl:ml-0 rtl:mr-auto"></i>
                        @endif
                    </a>
                    <a href="{{ route('language.switch', 'ar') }}" class="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors {{ app()->getLocale() === 'ar' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : '' }}">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiMwMDczM0UiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjEwIiB3aWR0aD0iMjAiIGhlaWdodD0iNSIgZmlsbD0iIzAwNzMzRSIvPgo8L3N2Zz4K" alt="Arabic" class="w-5 h-4">
                        <span>العربية</span>
                        @if(app()->getLocale() === 'ar')
                            <i class="fas fa-check text-blue-500 ml-auto rtl:ml-0 rtl:mr-auto"></i>
                        @endif
                    </a>
                </div> --}}

            </div>

            <!-- Theme Toggle -->
            <button id="theme-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                <i class="fas fa-sun dark:hidden text-lg"></i>
                <i class="fas fa-moon hidden dark:block text-lg"></i>
            </button>

            <!-- Notifications -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-bell text-lg"></i>
                    @if(isset($unreadNotificationsCount) && $unreadNotificationsCount > 0)
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                            {{ $unreadNotificationsCount > 9 ? '9+' : $unreadNotificationsCount }}
                        </span>
                    @endif
                </button>
                
                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                    <!-- Notifications Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ __('messages.notifications') }}</h3>
                        @if(isset($unreadNotificationsCount) && $unreadNotificationsCount > 0)
                            <button class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                {{ __('messages.mark_as_read') }}
                            </button>
                        @endif
                    </div>
                    
                    <!-- Notifications List -->
                    <div class="max-h-96 overflow-y-auto">
                        @if(isset($notifications) && count($notifications) > 0)
                            @foreach($notifications as $notification)
                                <div class="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors {{ $notification->read_at ? '' : 'bg-blue-50 dark:bg-blue-900/20' }}">
                                    <div class="flex items-start space-x-3 rtl:space-x-reverse">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                                <i class="fas fa-{{ $notification->icon ?? 'bell' }} text-blue-600 dark:text-blue-400 text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $notification->title }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $notification->message }}</p>
                                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-2">{{ $notification->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="p-8 text-center">
                                <i class="fas fa-bell-slash text-gray-400 text-3xl mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400">{{ __('messages.no_new_notifications') }}</p>
                            </div>
                        @endif
                    </div>
                    
                    <!-- View All Link -->
                    @if(isset($notifications) && count($notifications) > 0)
                        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                            <a href="{{ route('notifications.index') }}" class="block text-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                {{ __('messages.view_all') }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Profile Dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center space-x-3 rtl:space-x-reverse p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        {{-- @if(auth()->user()->avatar)
                            <img src="{{ auth()->user()->avatar }}" alt="{{ auth()->user()->name }}" class="w-8 h-8 rounded-full object-cover">
                        @else
                            <span class="text-white text-sm font-medium">{{ substr(auth()->user()->name ?? 'U', 0, 1) }}</span>
                        @endif --}}
                    </div>
                    <div class="hidden sm:block text-left rtl:text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ auth()->user()->name ?? 'User' }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->role ?? 'Admin' }}</p>
                    </div>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                
                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-50">
                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ auth()->user()->name ?? 'User' }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                    </div>
                    
                    <a href="{{ route('profile.edit') }}" class="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-user-circle w-4"></i>
                        <span>{{ __('messages.profile') }}</span>
                    </a>
                    
                    {{-- <a href="{{ route('settings.index') }}" class="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-cog w-4"></i>
                        <span>{{ __('messages.settings') }}</span>
                    </a> --}}
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                            <i class="fas fa-sign-out-alt w-4"></i>
                            <span>{{ __('messages.logout') }}</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Alpine.js for dropdowns -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'tenant_id' => 'sometimes|required|exists:tenants,id',
            'branch_id' => 'sometimes|required|exists:branches,id',
            'name' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:50',
            'description' => 'nullable|string|max:1000',
            'menu_type' => 'sometimes|required|in:breakfast,lunch,dinner,all_day,seasonal,special',
            'start_time' => 'nullable|date_format:H:i:s',
            'end_time' => 'nullable|date_format:H:i:s|after:start_time',
            'available_days' => 'nullable|json',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'tenant_id.required' => 'Tenant is required.',
            'tenant_id.exists' => 'Selected tenant does not exist.',
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.required' => 'Menu name is required.',
            'name.max' => 'Menu name cannot exceed 255 characters.',
            'code.required' => 'Menu code is required.',
            'code.max' => 'Menu code cannot exceed 50 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'menu_type.required' => 'Menu type is required.',
            'menu_type.in' => 'Menu type must be one of: breakfast, lunch, dinner, all_day, seasonal, special.',
            'start_time.date_format' => 'Start time must be in HH:MM:SS format.',
            'end_time.date_format' => 'End time must be in HH:MM:SS format.',
            'end_time.after' => 'End time must be after start time.',
            'available_days.json' => 'Available days must be in valid JSON format.',
            'sort_order.integer' => 'Sort order must be a valid number.',
            'sort_order.min' => 'Sort order must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'branch_id' => 'branch',
            'menu_type' => 'menu type',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'available_days' => 'available days',
            'is_active' => 'active status',
            'is_default' => 'default status',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => filter_var($this->is_active, FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('is_default')) {
            $this->merge([
                'is_default' => filter_var($this->is_default, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
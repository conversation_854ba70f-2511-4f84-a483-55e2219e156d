@extends('layouts.app')

@section('title', 'Menus')

@section('content')

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4>Menus</h4>
        <button class="btn btn-primary" onclick="openModal('createMenuModal', 'Create Menu')">Create New</button>
    </div>
    <div class="card-body">
        <table id="menusTable" class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    {{-- <th>Code</th> --}}
            
                    <th>Menu Type</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createMenuModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Menu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createMenuForm" method="POST" action="{{ route('menus.store') }}">
                    @csrf
                   
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">Code</label>
                                <input type="text" class="form-control" id="code" name="code">
                                <small class="form-text text-muted">Leave empty to auto-generate</small>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="menu_type" class="form-label">Menu Type</label>
                                <select class="form-control" id="menu_type" name="menu_type" required>
                                    <option value="">Select Type</option>
                                    <option value="breakfast">Breakfast</option>
                                    <option value="lunch">Lunch</option>
                                    <option value="dinner">Dinner</option>
                                    <option value="all_day">All Day</option>
                                    <option value="seasonal">Seasonal</option>
                                    <option value="special">Special</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1">
                                    <label class="form-check-label" for="is_default">
                                        Default Menu
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" form="createMenuForm" class="btn btn-primary">Create</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editMenuModal" tabindex="-1" aria-labelledby="editMenuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMenuModalLabel">Edit Menu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editMenuForm" method="POST" action="">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="edit_menu_id" name="menu_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tenant</label>
                                <div class="form-control-plaintext">{{ $currentTenant->name ?? 'N/A' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Branch</label>
                                <div class="form-control-plaintext">{{ $currentBranch->name ?? 'N/A' }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_code" class="form-label">Code</label>
                                <input type="text" class="form-control" id="edit_code" name="code">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_menu_type" class="form-label">Menu Type</label>
                                <select class="form-control" id="edit_menu_type" name="menu_type" required>
                                    <option value="">Select Type</option>
                                    <option value="breakfast">Breakfast</option>
                                    <option value="lunch">Lunch</option>
                                    <option value="dinner">Dinner</option>
                                    <option value="all_day">All Day</option>
                                    <option value="seasonal">Seasonal</option>
                                    <option value="special">Special</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                                    <label class="form-check-label" for="edit_is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_default" name="is_default" value="1">
                                    <label class="form-check-label" for="edit_is_default">
                                        Default Menu
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" form="editMenuForm" class="btn btn-primary">Update</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        initDataTable('menusTable', '{{ route('menus.index') }}', [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name' },

            { data: 'menu_type' },
            { data: 'status' },
            { data: 'actions', orderable: false }
        ]);

        // Clear validation errors when modal is hidden
        $('#createMenuModal, #editMenuModal').on('hidden.bs.modal', function() {
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').remove();
        });

        // Handle create form submission
        $('#createMenuForm').on('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            
            // Clear previous validation errors
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').remove();
            
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#createMenuModal').modal('hide');
                    $('#menusTable').DataTable().ajax.reload();
                    Toast.success('Menu created successfully');
                    $('#createMenuForm')[0].reset();
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        const errors = xhr.responseJSON.errors;
                        for (const [field, messages] of Object.entries(errors)) {
                            const inputField = $(`#createMenuForm [name="${field}"]`);
                            if (inputField.length) {
                                inputField.addClass('is-invalid');
                                const feedbackDiv = $('<div class="invalid-feedback"></div>').text(messages[0]);
                                inputField.after(feedbackDiv);
                            }
                        }
                    } else {
                        Toast.error('An error occurred while creating the menu');
                    }
                }
            });
        });

        // Handle edit form submission
        $('#editMenuForm').on('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            
            // Clear previous validation errors
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').remove();
            
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#editMenuModal').modal('hide');
                    $('#menusTable').DataTable().ajax.reload();
                    Toast.success('Menu updated successfully');
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        const errors = xhr.responseJSON.errors;
                        for (const [field, messages] of Object.entries(errors)) {
                            const inputField = $(`#editMenuForm [name="${field}"]`);
                            if (inputField.length) {
                                inputField.addClass('is-invalid');
                                const feedbackDiv = $('<div class="invalid-feedback"></div>').text(messages[0]);
                                inputField.after(feedbackDiv);
                            }
                        }
                    } else {
                        Toast.error('An error occurred while updating the menu');
                    }
                }
            });
        });

        // Setup edit button click handlers
        $('#menusTable').on('click', '.edit-btn', function() {
            const id = $(this).data('id');
            
            // Load menu data and open edit modal
            $.ajax({
                url: `{{ url('menu/menus') }}/${id}/edit`,
                type: 'GET',
                success: function(data) {
                    $('#editMenuForm').attr('action', `{{ url('menu/menus') }}/${id}`);
                    $('#edit_menu_id').val(data.id);
                    $('#edit_name').val(data.name);
                    $('#edit_code').val(data.code);
                    $('#edit_description').val(data.description);
                    $('#edit_menu_type').val(data.menu_type);
                    $('#edit_sort_order').val(data.sort_order);
                    $('#edit_is_active').prop('checked', data.is_active == 1);
                    $('#edit_is_default').prop('checked', data.is_default == 1);
                    $('#editMenuModal').modal('show');
                },
                error: function() {
                    Toast.error('Failed to load menu data');
                }
            });
        });

        // Setup delete button click handlers
        $('#menusTable').on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            const menuName = $(this).data('name') || 'this menu';
            
            Confirm.show(
                'Confirm Deletion',
                `Are you sure you want to delete "${menuName}"? This action cannot be undone.`,
                function() {
                    $.ajax({
                        url: `{{ url('menu/menus') }}/${id}`,
                        type: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            $('#menusTable').DataTable().ajax.reload();
                            Toast.success('Menu deleted successfully');
                        },
                        error: function(xhr) {
                            if (xhr.status === 400) {
                                Toast.error(xhr.responseJSON.message || 'Cannot delete menu');
                            } else {
                                Toast.error('An error occurred while deleting the menu');
                            }
                        }
                    });
                }
            );
        });
    });
</script>
@endpush
@endsection

@extends('layouts.app')

@section('title', 'Menu Items')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4>Menu Items</h4>
        <button class="btn btn-primary" onclick="openModal('createMenuItemModal', 'Create Menu Item')">Create New</button>
    </div>
    
    <!-- Filters -->
    <div class="card-body border-bottom">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Filter by Menu</label>
                <select id="menuFilter" class="form-select">
                    <option value="">All Menus</option>
                    @foreach($menus as $menu)
                        <option value="{{ $menu->id }}">{{ $menu->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Filter by Category</label>
                <select id="categoryFilter" class="form-select">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Status</label>
                <select id="statusFilter" class="form-select">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Featured</label>
                <select id="featuredFilter" class="form-select">
                    <option value="">All Items</option>
                    <option value="1">Featured</option>
                    <option value="0">Not Featured</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Spicy</label>
                <select id="spicyFilter" class="form-select">
                    <option value="">All Items</option>
                    <option value="1">Spicy</option>
                    <option value="0">Not Spicy</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        <table id="menuItemsTable" class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Code</th>
                    <th>Menu</th>
                    <th>Category</th>
                    <th>Base Price</th>
                    <th>Cost Price</th>
                    <th>Prep Time</th>
                    <th>Status</th>
                    <th>Featured</th>
                    <th>Spicy</th>
                    <th>Dietary Info</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createMenuItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Menu Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createMenuItemForm" action="{{ route('menu-items.store') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Menu <span class="text-danger">*</span></label>
                                <select name="menu_id" class="form-control" required>
                                    <option value="">Select Menu</option>
                                    @foreach($menus as $menu)
                                        <option value="{{ $menu->id }}">{{ $menu->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Category <span class="text-danger">*</span></label>
                                <select name="category_id" class="form-control" required>
                                    <option value="">Select Category</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Base Price <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" name="base_price" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Cost Price</label>
                                <input type="number" step="0.01" name="cost_price" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Prep Time (minutes)</label>
                                <input type="number" name="prep_time_minutes" class="form-control" min="0" max="300">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SKU</label>
                                <input type="text" name="sku" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Barcode</label>
                                <input type="text" name="barcode" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Short Description</label>
                        <textarea name="short_description" class="form-control" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Calories</label>
                                <input type="number" name="calories" class="form-control" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sort Order</label>
                                <input type="number" name="sort_order" class="form-control" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="is_active" value="1" checked>
                                <label class="form-check-label">Active</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="is_featured" value="1">
                                <label class="form-check-label">Featured</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="is_spicy" value="1" id="isSpicyCreate">
                                <label class="form-check-label">Spicy</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Spice Level (1-5)</label>
                                <input type="number" name="spice_level" class="form-control" min="1" max="5" id="spiceLevelCreate" disabled>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editMenuItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Menu Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Form loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewMenuItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Menu Item Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        const table = initDataTable('menuItemsTable', '{{ route('menu-items.index') }}', [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'menu_name', name: 'menu_name', orderable: false },
            { data: 'category_name', name: 'category_name', orderable: false },
            { data: 'base_price_formatted', name: 'base_price' },
            { data: 'cost_price_formatted', name: 'cost_price' },
            { data: 'prep_time_formatted', name: 'prep_time_minutes' },
            { data: 'status_badge', name: 'is_active', orderable: false },
            { data: 'featured_badge', name: 'is_featured', orderable: false },
            { data: 'spicy_badge', name: 'is_spicy', orderable: false },
            { data: 'dietary_info_badges', name: 'dietary_info', orderable: false, searchable: false },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false 
            }
        ]);

        // Filter handlers
        $('#menuFilter, #categoryFilter, #statusFilter, #featuredFilter, #spicyFilter').on('change', function() {
            table.ajax.reload();
        });

        // Override the DataTable ajax data to include filters
        table.on('preXhr.dt', function(e, settings, data) {
            data.menu_id = $('#menuFilter').val();
            data.category_id = $('#categoryFilter').val();
            data.is_active = $('#statusFilter').val();
            data.is_featured = $('#featuredFilter').val();
            data.is_spicy = $('#spicyFilter').val();
        });

        // Load categories based on selected menu for create form
        $('select[name="menu_id"]').on('change', function() {
            const menuId = $(this).val();
            const categorySelect = $(this).closest('form').find('select[name="category_id"]');
            
            categorySelect.empty().append('<option value="">Select Category</option>');
            
            if (menuId) {
                fetch(`/api/menus/${menuId}/categories`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            data.data.forEach(category => {
                                categorySelect.append(`<option value="${category.id}">${category.name}</option>`);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading categories:', error);
                    });
            }
        });

        // Handle spicy checkbox for create form
        $('#isSpicyCreate').on('change', function() {
            $('#spiceLevelCreate').prop('disabled', !this.checked);
            if (!this.checked) {
                $('#spiceLevelCreate').val('');
            }
        });

        // Handle create form submission
        submitForm('createMenuItemForm', function(data) {
            $('#createMenuItemModal').modal('hide');
            table.ajax.reload();
            Toast.success('Menu item created successfully');
        }, function(errors) {
            console.log(errors);
        });

        // Handle edit button clicks
        $(document).on('click', '.edit-menu-item', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('menu-items.show', ':id') }}'.replace(':id', id);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const menuItem = data.data;
                        $('#editMenuItemModal .modal-body').html(`
                            <form id="editMenuItemForm" action="{{ route('menu-items.update', ':id') }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Menu <span class="text-danger">*</span></label>
                                            <select name="menu_id" class="form-control" required>
                                                <option value="${menuItem.menu_id}" selected>${menuItem.menu ? menuItem.menu.name : 'N/A'}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Category <span class="text-danger">*</span></label>
                                            <select name="category_id" class="form-control" required>
                                                <option value="${menuItem.category_id}" selected>${menuItem.category ? menuItem.category.name : 'N/A'}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">Name <span class="text-danger">*</span></label>
                                            <input type="text" name="name" class="form-control" value="${menuItem.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Code <span class="text-danger">*</span></label>
                                            <input type="text" name="code" class="form-control" value="${menuItem.code}" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Base Price <span class="text-danger">*</span></label>
                                            <input type="number" step="0.01" name="base_price" class="form-control" value="${menuItem.base_price}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Cost Price</label>
                                            <input type="number" step="0.01" name="cost_price" class="form-control" value="${menuItem.cost_price || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Prep Time (minutes)</label>
                                            <input type="number" name="prep_time_minutes" class="form-control" value="${menuItem.prep_time_minutes || ''}" min="0" max="300">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SKU</label>
                                            <input type="text" name="sku" class="form-control" value="${menuItem.sku || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Barcode</label>
                                            <input type="text" name="barcode" class="form-control" value="${menuItem.barcode || ''}">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea name="description" class="form-control" rows="3">${menuItem.description || ''}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Short Description</label>
                                    <textarea name="short_description" class="form-control" rows="2">${menuItem.short_description || ''}</textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Calories</label>
                                            <input type="number" name="calories" class="form-control" value="${menuItem.calories || ''}" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Sort Order</label>
                                            <input type="number" name="sort_order" class="form-control" value="${menuItem.sort_order || 0}" min="0">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_active" value="1" ${menuItem.is_active ? 'checked' : ''}>
                                            <label class="form-check-label">Active</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_featured" value="1" ${menuItem.is_featured ? 'checked' : ''}>
                                            <label class="form-check-label">Featured</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_spicy" value="1" id="isSpicyEdit" ${menuItem.is_spicy ? 'checked' : ''}>
                                            <label class="form-check-label">Spicy</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Spice Level (1-5)</label>
                                            <input type="number" name="spice_level" class="form-control" value="${menuItem.spice_level || ''}" min="1" max="5" id="spiceLevelEdit" ${!menuItem.is_spicy ? 'disabled' : ''}>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Update</button>
                            </form>
                        `.replace(':id', id));
                        
                        // Load menus and categories for edit form
                        loadMenusForEdit(menuItem.menu_id);
                        loadCategoriesForEdit(menuItem.menu_id, menuItem.category_id);
                        
                        // Handle spicy checkbox for edit form
                        $('#isSpicyEdit').on('change', function() {
                            $('#spiceLevelEdit').prop('disabled', !this.checked);
                            if (!this.checked) {
                                $('#spiceLevelEdit').val('');
                            }
                        });
                        
                        $('#editMenuItemModal').modal('show');
                        
                        // Handle edit form submission
                        submitForm('editMenuItemForm', function(data) {
                            $('#editMenuItemModal').modal('hide');
                            table.ajax.reload();
                            Toast.success('Menu item updated successfully');
                        }, function(errors) {
                            console.log(errors);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('Failed to load menu item data');
                });
        });

        // Handle view button clicks
        $(document).on('click', '.view-menu-item', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('menu-items.show', ':id') }}'.replace(':id', id);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const menuItem = data.data;
                        const dietaryInfo = menuItem.dietary_info && Array.isArray(menuItem.dietary_info) 
                            ? menuItem.dietary_info.map(info => `<span class="badge bg-info me-1">${info}</span>`).join('')
                            : 'None';
                        const allergens = menuItem.allergens && Array.isArray(menuItem.allergens)
                            ? menuItem.allergens.join(', ')
                            : 'None';
                            
                        $('#viewMenuItemModal .modal-body').html(`
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Basic Information</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Name:</strong></td><td>${menuItem.name}</td></tr>
                                        <tr><td><strong>Code:</strong></td><td>${menuItem.code}</td></tr>
                                        <tr><td><strong>Menu:</strong></td><td>${menuItem.menu ? menuItem.menu.name : 'N/A'}</td></tr>
                                        <tr><td><strong>Category:</strong></td><td>${menuItem.category ? menuItem.category.name : 'N/A'}</td></tr>
                                        <tr><td><strong>SKU:</strong></td><td>${menuItem.sku || 'N/A'}</td></tr>
                                        <tr><td><strong>Barcode:</strong></td><td>${menuItem.barcode || 'N/A'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Pricing & Details</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Base Price:</strong></td><td>$${parseFloat(menuItem.base_price).toFixed(2)}</td></tr>
                                        <tr><td><strong>Cost Price:</strong></td><td>${menuItem.cost_price ? '$' + parseFloat(menuItem.cost_price).toFixed(2) : 'N/A'}</td></tr>
                                        <tr><td><strong>Prep Time:</strong></td><td>${menuItem.prep_time_minutes ? menuItem.prep_time_minutes + ' min' : 'N/A'}</td></tr>
                                        <tr><td><strong>Calories:</strong></td><td>${menuItem.calories || 'N/A'}</td></tr>
                                        <tr><td><strong>Sort Order:</strong></td><td>${menuItem.sort_order}</td></tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Status & Features</h6>
                                    <p>
                                        ${menuItem.is_active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>'}
                                        ${menuItem.is_featured ? '<span class="badge bg-warning ms-1">Featured</span>' : ''}
                                        ${menuItem.is_spicy ? '<span class="badge bg-danger ms-1">🌶️ Spicy' + (menuItem.spice_level ? ' (Level ' + menuItem.spice_level + ')' : '') + '</span>' : ''}
                                    </p>
                                </div>
                            </div>
                            
                            ${menuItem.description ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Description</h6>
                                    <p>${menuItem.description}</p>
                                </div>
                            </div>
                            ` : ''}
                            
                            ${menuItem.short_description ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Short Description</h6>
                                    <p>${menuItem.short_description}</p>
                                </div>
                            </div>
                            ` : ''}
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>Dietary Information</h6>
                                    <p>${dietaryInfo}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Allergens</h6>
                                    <p>${allergens}</p>
                                </div>
                            </div>
                        `);
                        
                        $('#viewMenuItemModal').modal('show');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('Failed to load menu item details');
                });
        });

        // Handle delete button clicks
        $(document).on('click', '.delete-menu-item', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const url = '{{ route('menu-items.destroy', ':id') }}'.replace(':id', id);
            
            confirmDelete(url, function() {
                table.ajax.reload();
            });
        });

        // Helper functions
        function loadMenusForEdit(selectedMenuId) {
            const menuSelect = $('#editMenuItemForm select[name="menu_id"]');
            menuSelect.empty();
            
            @foreach($menus as $menu)
                menuSelect.append(`<option value="{{ $menu->id }}" ${selectedMenuId == {{ $menu->id }} ? 'selected' : ''}>{{ $menu->name }}</option>`);
            @endforeach
        }

        function loadCategoriesForEdit(menuId, selectedCategoryId) {
            const categorySelect = $('#editMenuItemForm select[name="category_id"]');
            categorySelect.empty();
            
            if (menuId) {
                fetch(`/api/menus/${menuId}/categories`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            data.data.forEach(category => {
                                const selected = selectedCategoryId == category.id ? 'selected' : '';
                                categorySelect.append(`<option value="${category.id}" ${selected}>${category.name}</option>`);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading categories:', error);
                        // Fallback to all categories
                        @foreach($categories as $category)
                            categorySelect.append(`<option value="{{ $category->id }}" ${selectedCategoryId == {{ $category->id }} ? 'selected' : ''}>{{ $category->name }}</option>`);
                        @endforeach
                    });
            }
        }
    });
</script>
@endpush
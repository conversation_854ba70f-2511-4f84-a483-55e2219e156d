<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;
use Yajra\DataTables\Facades\DataTables;

class AddonWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $addons = $this->menuService->getAllAddons();
            
            return DataTables::of($addons)
                ->addColumn('menu_item_name', function ($addon) {
                    return $addon->menuItem ? $addon->menuItem->name : 'N/A';
                })
                ->addColumn('category_name', function ($addon) {
                    return $addon->menuItem && $addon->menuItem->category ? $addon->menuItem->category->name : 'N/A';
                })
                ->addColumn('price_formatted', function ($addon) {
                    return '$' . number_format($addon->price, 2);
                })
                ->addColumn('is_required_badge', function ($addon) {
                    return $addon->is_required 
                        ? '<span class="badge bg-success">Required</span>' 
                        : '<span class="badge bg-secondary">Optional</span>';
                })
                ->addColumn('actions', function ($addon) {
                    return '
                        <button type="button" class="btn btn-sm btn-primary edit-addon" 
                                data-id="' . $addon->id . '">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-addon" 
                                data-id="' . $addon->id . '">
                            <i class="fas fa-trash"></i>
                        </button>
                    ';
                })
                ->rawColumns(['is_required_badge', 'actions'])
                ->make(true);
        }

        return view('menu::addons.index');
    }

    public function store(StoreAddonRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $addon = $this->menuService->createAddon($data['menu_item_id'], $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Addon created successfully',
                'data' => $addon
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating addon: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(string $id): JsonResponse
    {
        try {
            $addon = \App\Models\MenuItemAddon::with(['menuItem.category'])->findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $addon
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Addon not found'
            ], 404);
        }
    }

    public function update(UpdateAddonRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $addon = $this->menuService->updateAddon($id, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Addon updated successfully',
                'data' => $addon
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating addon: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(string $id): JsonResponse
    {
        try {
            $this->menuService->deleteAddon($id);
            return response()->json([
                'success' => true,
                'message' => 'Addon deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting addon: ' . $e->getMessage()
            ], 500);
        }
    }
}
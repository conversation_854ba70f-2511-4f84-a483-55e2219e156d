<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ config('app.name') }}">

    <title>@yield('title', __('messages.dashboard')) - {{ config('app.name') }}</title>
    <meta name="description" content="@yield('description', __('messages.restaurant_pos') . ' - ' . __('messages.dashboard'))">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Fonts with performance optimization -->
    <link rel="preload" href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet"></noscript>

    <!-- Icons with performance optimization -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"></noscript>

    <!-- Charts - Load only when needed -->
    @stack('chart-scripts')

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f8fafc;
            --dark-bg: #0f172a;
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
            --navbar-height: 64px;
            --transition-speed: 0.3s;
            --border-radius: 8px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .dark {
            --light-bg: #0f172a;
            --dark-bg: #f8fafc;
        }

        /* Performance optimizations */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }

        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--light-bg);
            transition: background-color var(--transition-speed) ease;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            overflow-x: hidden;
        }

        .dark body {
            background-color: var(--dark-bg);
            color: #e2e8f0;
        }

        /* Focus styles for accessibility */
        *:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Skip to main content for accessibility */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 9999;
        }

        .skip-link:focus {
            top: 6px;
        }
        
        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            top: 0;
            background: #1e293b;
            transition: all 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
            /* scrollbar-width: thin; */
        }
        
        .dark .sidebar {
            background: #0f172a;
        }
        
        .sidebar::-webkit-scrollbar {
        width: 0px; /* Hide scrollbar */
    }
        
        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }
        
        .sidebar.collapsed .sidebar-text {
            display: none;
        }
        
        .sidebar-left {
            left: 0;
        }
        
        .sidebar-right {
            right: 0;
        }
        
        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            will-change: margin;
        }

        .main-content.sidebar-collapsed {
            margin-left: var(--sidebar-collapsed-width);
        }

        .main-content.rtl {
            margin-left: 0;
            margin-right: var(--sidebar-width);
        }

        .main-content.rtl.sidebar-collapsed {
            margin-right: var(--sidebar-collapsed-width);
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                margin-right: 0;
            }

            .main-content.rtl {
                margin-left: 0;
                margin-right: 0;
            }
        }
        
        /* Navbar */
        .navbar {
            height: var(--navbar-height);
            background: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            position: sticky;
            top: 0;
            z-index: 999;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .dark .navbar {
            background: #1e293b;
            border-bottom-color: #334155;
        }
        
        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .dark .card {
            background: #1e293b;
            border: 1px solid #334155;
        }
        
        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1000;
            }
            .sidebar.rtl {
                transform: translateX(100%);
            }
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0 !important;
            }
            .main-content.rtl {
                margin-right: 0 !important;
            }
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
    
    @stack('styles')
</head>
<body class="{{ session('theme', 'light') }}" role="document">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">{{ __('messages.skip_to_main_content') }}</a>

    <!-- Mobile Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden" aria-hidden="true"></div>

    <!-- Sidebar -->
    @include('components.sidebar')

    <!-- Main Content -->
    <div id="main-content" class="main-content {{ app()->getLocale() === 'ar' ? 'rtl' : '' }}" role="main">
        <!-- Navbar -->
        @include('components.navbar')

        <!-- Page Content -->
        <main class="flex-1 p-4 md:p-6" id="page-content" tabindex="-1">
            <!-- Breadcrumb for screen readers -->
            <nav aria-label="{{ __('messages.breadcrumb') }}" class="sr-only">
                <ol>
                    <li><a href="{{ route('dashboard') }}">{{ __('messages.dashboard') }}</a></li>
                    @if(isset($breadcrumbs))
                        @foreach($breadcrumbs as $breadcrumb)
                            <li>{{ $breadcrumb['title'] }}</li>
                        @endforeach
                    @endif
                </ol>
            </nav>

            <!-- Page Title for screen readers -->
            <h1 class="sr-only">@yield('title', __('messages.dashboard'))</h1>

            <!-- Flash Messages -->
            @if(session('success'))
                <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg" role="alert" aria-live="polite">
                    <i class="fas fa-check-circle mr-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg" role="alert" aria-live="assertive">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {{ session('error') }}
                </div>
            @endif

            @if(session('warning'))
                <div class="mb-4 p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg" role="alert" aria-live="polite">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    {{ session('warning') }}
                </div>
            @endif

            <!-- Main Content Area -->
            <div class="fade-in">
                @yield('content')
            </div>
        </main>
    </div>
    
    <!-- Scripts -->
    <script>
        // Performance optimization: Use requestAnimationFrame for DOM updates
        const raf = window.requestAnimationFrame || window.setTimeout;

        // Error handling wrapper
        function safeExecute(fn, context = 'Unknown') {
            try {
                return fn();
            } catch (error) {
                console.error(`Error in ${context}:`, error);
                return null;
            }
        }

        // Theme Management with performance optimization
        function initTheme() {
            const themeToggle = document.getElementById('theme-toggle');
            const body = document.body;

            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            body.className = savedTheme;

            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    safeExecute(() => {
                        const currentTheme = body.classList.contains('dark') ? 'dark' : 'light';
                        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                        raf(() => {
                            body.className = newTheme;
                            localStorage.setItem('theme', newTheme);
                        });

                        // Update server session with error handling
                        fetch('/theme-toggle', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || ''
                            },
                            body: JSON.stringify({ theme: newTheme })
                        }).catch(error => console.warn('Theme sync failed:', error));
                    }, 'Theme toggle');
                });
            }
        }

        // Sidebar Management with performance optimization
        function initSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const mobileOverlay = document.getElementById('mobile-overlay');

            if (!sidebar || !mainContent) return;

            // Mobile overlay click
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', () => {
                    safeExecute(() => {
                        raf(() => {
                            sidebar.classList.remove('mobile-open');
                            mobileOverlay.classList.add('hidden');
                        });
                    }, 'Mobile overlay click');
                });
            }

            // Load saved sidebar state
            const sidebarCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            if (sidebarCollapsed && window.innerWidth >= 768) {
                raf(() => {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('sidebar-collapsed');
                });
            }
        }

        // Submenu Management
        function initSubmenus() {
            document.querySelectorAll('.submenu-toggle').forEach(toggle => {
                toggle.addEventListener('click', (e) => {
                    safeExecute(() => {
                        e.preventDefault();
                        const submenu = toggle.nextElementSibling;
                        const icon = toggle.querySelector('.submenu-icon');

                        if (submenu) {
                            raf(() => {
                                submenu.classList.toggle('hidden');
                                if (icon) {
                                    icon.style.transform = submenu.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(90deg)';
                                }
                            });
                        }
                    }, 'Submenu toggle');
                });
            });
        }

        // Global Search
        function initGlobalSearch() {
            const searchInput = document.getElementById('global-search');
            const searchResults = document.getElementById('search-results');

            if (searchInput && searchResults) {
                let searchTimeout;

                searchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    const query = e.target.value.trim();

                    if (query.length < 2) {
                        searchResults.classList.add('hidden');
                        return;
                    }

                    searchTimeout = setTimeout(() => {
                        safeExecute(() => {
                            // Implement search functionality here
                            searchResults.classList.remove('hidden');
                        }, 'Global search');
                    }, 300);
                });

                // Close search results when clicking outside
                document.addEventListener('click', (e) => {
                    if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                        searchResults.classList.add('hidden');
                    }
                });
            }
        }

        // Auto-hide flash messages
        function initFlashMessages() {
            setTimeout(() => {
                document.querySelectorAll('[role="alert"]').forEach(alert => {
                    if (!alert.classList.contains('permanent')) {
                        raf(() => {
                            alert.style.opacity = '0';
                            alert.style.transform = 'translateY(-10px)';
                            setTimeout(() => alert.remove(), 300);
                        });
                    }
                });
            }, 5000);
        }

        // Initialize everything when DOM is ready
        function init() {
            safeExecute(initTheme, 'Theme initialization');
            safeExecute(initSidebar, 'Sidebar initialization');
            safeExecute(initSubmenus, 'Submenus initialization');
            safeExecute(initGlobalSearch, 'Global search initialization');
            safeExecute(initFlashMessages, 'Flash messages initialization');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }

        // Performance monitoring (development only)
        @if(config('app.debug'))
            window.addEventListener('load', () => {
                if (window.performance) {
                    const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                    console.log(`Dashboard loaded in ${loadTime}ms`);
                }
            });
        @endif
    </script>

    <!-- Dashboard Script -->
    <script src="{{ asset('dashboard.js') }}" defer></script>

    @stack('scripts')
</body>
</html>
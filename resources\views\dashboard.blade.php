@extends('layouts.app')

@section('content')

    <!-- Content -->
    <div class="content">
        <!-- Stats Cards -->
        <div class="dashboard-grid">
            <div class="card">
                <div class="stat-card">
                    <div class="stat-icon primary">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3>245</h3>
                        <p>Today's Orders</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="stat-card">
                    <div class="stat-icon success">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3>$12,450</h3>
                        <p>Today's Revenue</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3>4.8</h3>
                        <p>Average Rating</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3>5</h3>
                        <p>Low Stock Items</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title">Sales Overview</h2>
                <button class="navbar-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24">
                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                </button>
            </div>
            <div class="chart-placeholder">
                📊 Sales Chart Placeholder
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-header">
                    <h2 class="chart-title">Popular Items</h2>
                </div>
                <div class="chart-placeholder">
                    🍕 Popular Items Chart
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-header">
                    <h2 class="chart-title">Order Status</h2>
                </div>
                <div class="chart-placeholder">
                    📈 Order Status Chart
                </div>
            </div>
        </div>
    </div>

    

@endsection
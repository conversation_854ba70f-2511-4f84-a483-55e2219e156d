@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom RTL Support */
[dir="rtl"] {
    direction: rtl;
}

[dir="rtl"] .sidebar-left {
    left: auto;
    right: 0;
}

[dir="rtl"] .main-content {
    margin-left: 0;
    margin-right: var(--sidebar-width);
}

[dir="rtl"] .main-content.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

[dir="rtl"] .nav-item:hover {
    transform: translateX(-4px);
}

[dir="rtl"] .submenu {
    margin-left: 0;
    margin-right: 2rem;
}

/* Custom Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

/* Performance optimizations */
.sidebar,
.main-content,
.navbar {
    will-change: transform;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .sidebar {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }

    .sidebar.mobile-open {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    [dir="rtl"] .sidebar {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }

    [dir="rtl"] .sidebar.mobile-open {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus improvements */
.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Custom Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Status Badge Styles */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background-color: #dcfce7;
    color: #166534;
}

.dark .status-badge.success {
    background-color: #14532d;
    color: #bbf7d0;
}

.status-badge.warning {
    background-color: #fef3c7;
    color: #92400e;
}

.dark .status-badge.warning {
    background-color: #92400e;
    color: #fde68a;
}

.status-badge.danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.dark .status-badge.danger {
    background-color: #991b1b;
    color: #fecaca;
}

.status-badge.info {
    background-color: #dbeafe;
    color: #1e40af;
}

.dark .status-badge.info {
    background-color: #1e40af;
    color: #bfdbfe;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #374151;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
    
    .navbar {
        display: none !important;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid;
    }
    
    .btn {
        border: 2px solid;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
